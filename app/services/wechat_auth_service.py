"""微信认证服务 (WeChatAuthService)

专门处理所有与微信认证相关的流程，特别是PC端的扫码登录。

主要职责:
- **二维码生命周期管理**: 创建用于扫码登录的二维码，并管理其在Redis中的状态（等待、已扫码、已确认、已过期）。
- **微信回调处理**: 接收和验证来自微信服务器的回调请求，并根据事件类型（如用户扫码、关注）更新二维码状态。
- **手机号绑定**: 在用户扫码后，处理用户与手机号的绑定流程，包括验证码校验、用户创建或信息更新。
- **状态查询**: 提供接口供前端轮询二维码的当前状态。
- **与微信API集成**: 封装与微信公众平台API的交互细节。
"""

import json
import textwrap
import time
import xml.etree.ElementTree as ET

from fastapi import HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import timedelta

from app.config.auth_config import AuthConfig
from app.core.logging import logger
from app.db.redis import get_key, set_key
from app.exceptions.auth import (
    AuthErrorCodes,
    WeChatAuthError,
)
from app.schemas.auth import AuthenticationResult, QRCodeResponse
from app.schemas.user import WeChatUserInfo
from app.schemas.wechat import WeChatScanStatus
from app.services.interfaces.device_service_interface import IDeviceTrustService
from app.services.interfaces.sms_service_interface import ISmsService
from app.services.interfaces.token_service_interface import ITokenService
from app.services.interfaces.user_management_service_interface import IUserManagementService
from app.services.interfaces.wechat_auth_service_interface import IWeChatAuthService
from app.services.user_aggregation_service import UserAggregationService
from app.services.wechat_integration_service import WeChatIntegrationService


class WeChatAuthService(IWeChatAuthService):
    """
    微信认证服务实现类。
    """

    def __init__(
        self,
        token_service: ITokenService,
        user_management_service: IUserManagementService,
        device_trust_service: IDeviceTrustService,
        user_aggregation_service: UserAggregationService,
        auth_config: AuthConfig,
        wechat_integration_service: WeChatIntegrationService,
        sms_service: ISmsService,
    ):
        """
        初始化微信认证服务。

        Args:
            token_service (ITokenService): 令牌服务实例。
            user_management_service (IUserManagementService): 用户管理服务实例。
            device_trust_service (IDeviceTrustService): 设备信任服务实例。
            user_aggregation_service (UserAggregationService): 用户聚合服务实例。
            auth_config (AuthConfig): 认证配置实例。
            wechat_integration_service (WeChatIntegrationService): 微信集成服务实例。
        """
        self.auth_config = auth_config
        self.logger = logger
        self.wechat_integration = wechat_integration_service
        self.user_management = user_management_service
        self.token_service = token_service
        self.device_service = device_trust_service
        self.user_aggregation_service = user_aggregation_service
        self.sms_service = sms_service

    async def _get_scene_key(self, scene_str: str) -> str:
        """获取场景的Redis键"""
        return f"wechat:qr:{scene_str}"

    async def _dedent_text(self, text: str) -> str:
        """去除多行文本的公共缩进并去除首尾空白"""
        return textwrap.dedent(text).strip()

    async def create_qr_code(self) -> QRCodeResponse:
        """
        创建用于PC端扫码登录的微信二维码。

        此方法会：
        1. 调用微信API生成一个带有时效性的二维码Ticket。
        2. 在Redis中为该二维码创建一个初始状态为 "waiting" 的记录。
        3. 返回包含二维码URL和场景字符串 (scene_str) 的响应。

        Returns:
            QRCodeResponse: 包含二维码URL、场景字符串和过期时间的对象。

        Raises:
            WeChatAuthError: 如果调用微信API或与Redis交互失败。
        """
        try:
            # 生成场景字符串
            scene_str = self.wechat_integration.generate_scene_str()

            # 创建二维码
            wechat_info = await self.wechat_integration.create_qr_code_ticket(scene_str)

            # 存储场景状态
            scene_data = {
                "status": "waiting",
                "created_at": int(time.time()),
                "expire_at": int(time.time()) + wechat_info["expire_seconds"],
            }

            await set_key(
                await self._get_scene_key(scene_str),
                json.dumps(scene_data),
                expire=wechat_info["expire_seconds"],
            )

            return QRCodeResponse(
                scene_str=scene_str,
                qr_url=wechat_info["qr_url"],
                expire_seconds=wechat_info["expire_seconds"],
            )

        except Exception as e:
            self.logger.error(f"创建二维码失败: {e}")
            raise WeChatAuthError("创建二维码失败", AuthErrorCodes.QR_CODE_EXPIRED) from e

    async def check_scan_status(
        self, scene_str: str, request: Request, db: AsyncSession
    ) -> WeChatScanStatus:
        """
        检查并处理二维码的当前状态，这是轮询的核心。

        此方法会处理所有状态转换逻辑：
        - waiting: 初始状态。
        - scanned: 已扫码，从此状态开始处理用户和设备逻辑。
        - confirmed: 老用户且设备受信任，登录成功。
        - device_verification: 老用户但设备不受信任，需要设备验证。
        - bind_phone: 新用户，需要绑定手机。
        - expired: 二维码过期。

        Args:
            scene_str (str): 二维码场景字符串。
            request (Request): FastAPI请求对象，用于设备指纹识别。
            db (AsyncSession): 数据库会话。

        Returns:
            WeChatScanStatus: 包含当前状态和所有必要数据的对象。
        """
        scene_key = await self._get_scene_key(scene_str)
        scene_data_json = await get_key(scene_key)

        if not scene_data_json:
            return WeChatScanStatus(status="expired", message="二维码已过期")

        data = json.loads(scene_data_json)
        current_status = data.get("status", "unknown")
        openid = data.get("openid")

        # 如果状态不是 'scanned'，直接返回当前状态信息
        if current_status != "scanned" or not openid:
            auth_result = None
            auth_result_data = data.get("auth_result")
            if auth_result_data and isinstance(auth_result_data, dict):
                # 从Redis加载的字典需要重新验证为Pydantic模型
                auth_result = AuthenticationResult.model_validate(auth_result_data)

            auth_result = None
            auth_result_data = data.get("auth_result")
            if auth_result_data and isinstance(auth_result_data, dict):
                # 从Redis加载的字典需要重新验证为Pydantic模型
                auth_result = AuthenticationResult.model_validate(auth_result_data)

            # 如果状态是 confirmed，从 scan_response 中加载完整的状态
            if current_status == "confirmed" and "scan_response" in data:
                return WeChatScanStatus.model_validate(data["scan_response"])

            return WeChatScanStatus(
                status=current_status,
                openId=openid,
                message=data.get("message", ""),
            )

        # --- 核心处理逻辑：当状态为 'scanned' 时 ---
        self.logger.info(f"处理扫码状态 - scene: {scene_str}, openid: {openid}")

        # 检查用户是否存在
        user = await self.user_management.get_user_by_openid(openid, db)

        # 情况一：老用户
        if user:
            return await self._handle_existing_user_scan(user, scene_key, data, request, db)

        # 情况二：新用户
        else:
            return await self._handle_new_user_scan(openid, scene_key, data)

    async def _handle_existing_user_scan(
        self, user, scene_key: str, data: dict, request: Request, db: AsyncSession
    ) -> WeChatScanStatus:
        """处理已存在用户的扫码事件（跳过设备验证）"""
        self.logger.info(f"处理已存在用户的扫码 - 用户ID: {user.id}")

        # 直接创建访问令牌，不进行设备验证
        access_token = await self.token_service.create_access_token(
            data={"sub": user.username},
            device_id=None,  # device_id可以为None
        )
        # 创建刷新令牌
        try:
            refresh_expires = timedelta(minutes=self.auth_config.REFRESH_TOKEN_EXPIRE_MINUTES)
        except Exception:
            refresh_expires = timedelta(days=30)
        refresh_token = await self.token_service.create_token(
            username=user.username,
            device_id=None,
            expires_delta=refresh_expires,
            token_type="refresh",
        )
        aggregated_user = await self.user_aggregation_service.get_user_profile(db, user.id)
        await self.user_management.update_last_login(user.id, db)

        # 构造最终的、扁平化的状态响应
        final_status = WeChatScanStatus(
            status="confirmed",
            message="微信登录成功",
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            user=aggregated_user,
            openId=data.get("openid"),
        )

        # 更新Redis状态为 confirmed，并存储完整的、扁平化的响应
        data["status"] = "confirmed"
        # 使用 model_dump(mode='json') 来确保 datetime 等被正确序列化
        data["scan_response"] = json.loads(final_status.model_dump_json())
        await set_key(
            scene_key,
            json.dumps(data),
            expire=data["expire_at"] - int(time.time()),
        )

        return final_status

    async def _handle_new_user_scan(
        self, openid: str, scene_key: str, data: dict
    ) -> WeChatScanStatus:
        """处理新用户的扫码事件"""
        self.logger.info(f"处理新用户的扫码 - openid: {openid}")

        # 更新Redis状态为 bind_phone
        data["status"] = "bind_phone"
        await set_key(
            scene_key,
            json.dumps(data),
            expire=data["expire_at"] - int(time.time()),
        )

        return WeChatScanStatus(status="bind_phone", openId=openid, message="新用户，请绑定手机号")

    async def handle_wechat_callback(
        self, xml_data: str, signature: str, timestamp: str, nonce: str
    ) -> str:
        response_content = "success"

        try:
            if not self.wechat_integration.verify_signature(signature, timestamp, nonce):
                self.logger.warning("微信回调签名验证失败")
                # 即使签名失败，也返回"success"，防止恶意请求导致微信不断重试。
                return response_content

            msg_type, event, event_key, from_user, to_user = self._parse_wechat_xml(xml_data)
            logger.info(
                f"msg_type: {msg_type}, event: {event}, event_key: {event_key}, from_user: {from_user}, to_user: {to_user}"
            )

            # 检查是否是扫码登录事件
            is_scan_event = (
                msg_type == "event"
                and event in ["SCAN", "subscribe"]
                and event_key
                and event_key.startswith("login_")
            )

            if is_scan_event:
                scene_str = event_key
                openid = from_user
                logger.info(f"微信扫码事件 - scene: {scene_str}, openid: {openid}")
                await self._update_scene_status(scene_str, "scanned", openid)
                self.logger.info(f"微信扫码事件处理成功 - scene: {scene_str}")
                # 向用户微信客户端回复消息
                scan_success_text = """
                    🎉 扫码成功！
                    请继续在网页端操作。
                """
                response_content = self.wechat_integration.create_text_response(
                    to_user=from_user,
                    from_user=to_user,
                    content=await self._dedent_text(scan_success_text),
                )

        except Exception:
            self.logger.error("处理微信回调时发生未知错误", exc_info=True)
            # 即使内部出错，也向微信返回成功，避免微信重试。
        return response_content

    async def bind_phone_number(
        self, openid: str, phone: str, verification_code: str, request: Request, db: AsyncSession
    ) -> AuthenticationResult:
        """
        将微信 openid 与手机号绑定，并完成最终认证，与短信登录流程对齐。

        此方法在用户扫码后，作为新用户输入手机和验证码时被调用。
        它将复用短信认证的核心逻辑，包括用户创建、设备验证等。

        Args:
            openid (str): 用户的微信 openid。
            phone (str): 用户要绑定的手机号。
            verification_code (str): 手机收到的短信验证码。
            request (Request): FastAPI请求对象，用于设备指纹识别。
            db (AsyncSession): 数据库会话。

        Returns:
            AuthenticationResult: 包含完整认证结果的对象，可能需要设备验证。
        """
        # 1. 验证短信验证码
        from app.core.sms_template import SmsTemplate

        # 1. 验证短信验证码

        is_valid = await self.sms_service.verify_code(
            phone, verification_code, SmsTemplate.REGISTER
        )
        if not is_valid:
            raise WeChatAuthError("验证码错误或已过期", AuthErrorCodes.INVALID_VERIFICATION_CODE)

        # 2. 获取或创建用户（复用短信登录逻辑）
        is_new_user, user = await self.user_management.get_or_create_user_by_phone(phone, db)

        # 3. 绑定微信信息
        # 在绑定前，检查手机号是否已被其他微信绑定，或该微信是否已绑定其他手机
        if (user.wechat_openid and user.wechat_openid != openid) or (
            await self.user_management.get_user_by_openid(openid, db)
            and user.wechat_openid != openid
        ):
            raise WeChatAuthError("账号绑定冲突", AuthErrorCodes.BIND_CONFLICT)

        wechat_user_info = await self.wechat_integration.get_user_info(openid)
        await self._update_user_wechat_info(user, openid, wechat_user_info)
        await db.commit()
        await db.refresh(user)

        # 4. 评估设备信任状态（复用短信登录逻辑）
        trust_evaluation = await self.device_service.evaluate_device_trust(user, request, db)

        # 5. 根据设备状态返回结果（复用短信登录逻辑）
        if trust_evaluation.requires_verification:
            token = await self.device_service.generate_device_verification_token(
                user.id,
                trust_evaluation.device.id,
                phone,
                trust_evaluation.device.device_fingerprint,
            )
            return AuthenticationResult(
                success=True, requires_device_verification=True, verification_token=token
            )

        access_token = await self.token_service.create_access_token(
            data={"sub": user.username}, device_id=trust_evaluation.device.id
        )
        # 创建刷新令牌
        try:
            refresh_expires = timedelta(minutes=self.auth_config.REFRESH_TOKEN_EXPIRE_MINUTES)
        except Exception:
            refresh_expires = timedelta(days=30)
        refresh_token = await self.token_service.create_token(
            username=user.username,
            device_id=trust_evaluation.device.id,
            expires_delta=refresh_expires,
            token_type="refresh",
        )
        aggregated_user = await self.user_aggregation_service.get_user_profile(db, user.id)
        await self.user_management.update_last_login(user.id, db)

        return AuthenticationResult(
            success=True,
            user=aggregated_user,
            access_token=access_token,
            refresh_token=refresh_token,
            message="注册并登录成功" if is_new_user else "绑定成功",
        )

    def _parse_wechat_xml(self, xml_data: str) -> tuple[str, str, str, str, str]:
        """解析微信XML消息"""
        try:
            root = ET.fromstring(xml_data)
            result = {}
            for child in root:
                result[child.tag] = child.text
            logger.info(f"解析微信XML消息: {result}")
            return (
                result.get("MsgType"),
                result.get("Event"),
                result.get("EventKey"),
                result.get("FromUserName"),
                result.get("ToUserName"),
            )
        except ET.ParseError as e:
            raise WeChatAuthError("解析微信XML消息失败", AuthErrorCodes.WECHAT_API_ERROR) from e

    async def _update_scene_status(self, scene_str: str, status: str, openid: str | None = None):
        """更新场景状态"""
        scene_data = await get_key(await self._get_scene_key(scene_str))
        if not scene_data:
            return

        data = json.loads(scene_data)
        data["status"] = status
        if openid:
            data["openid"] = openid

        await set_key(
            await self._get_scene_key(scene_str),
            json.dumps(data),
            expire=data["expire_at"] - int(time.time()),
        )

    async def _create_success_message(self) -> str:
        """创建登录成功消息"""
        message = """
            🎉 扫码成功！
            请继续在网页端操作。
        """
        return self.wechat_integration.create_text_response(
            "", "", await self._dedent_text(message)
        )

    def _validate_bind_conflicts(
        self,
        existing_user_by_phone,
        existing_user_by_openid,
        phone: str,
        openid: str,
    ):
        """验证绑定冲突"""
        # 情况1: openid已绑定其他手机号
        if existing_user_by_openid and existing_user_by_openid.username != phone:
            raise HTTPException(
                status_code=400,
                detail=f"该微信账号已绑定手机号: {existing_user_by_openid.username}",
            )

        # 情况2: 手机号存在且已绑定其他openid
        if (
            existing_user_by_phone
            and existing_user_by_phone.wechat_openid
            and existing_user_by_phone.wechat_openid != openid
        ):
            raise HTTPException(status_code=400, detail="该手机号已绑定其他微信账号")

    async def _process_bind_logic(
        self,
        existing_user_by_phone,
        existing_user_by_openid,
        phone: str,
        openid: str,
        wechat_user_info: WeChatUserInfo,
        db: AsyncSession,
    ) -> tuple:
        """处理绑定逻辑"""
        user = None
        action_message = ""

        # 情况3: 手机号存在但未绑定微信，进行绑定
        if existing_user_by_phone and not existing_user_by_phone.wechat_openid:
            user = existing_user_by_phone
            await self._update_user_wechat_info(user, openid, wechat_user_info)
            user.login_type = "wechat"
            # 更新昵称（如果用户没有设置昵称）
            if not user.nickname:
                user.nickname = (
                    wechat_user_info.nickname
                    or await self.user_management._generate_random_nickname()
                )
            action_message = "微信绑定成功"

        # 情况4: openid和手机号都已存在且匹配，更新微信信息
        elif existing_user_by_openid and existing_user_by_openid.username == phone:
            user = existing_user_by_openid
            await self._update_user_wechat_info(user, openid, wechat_user_info, update_openid=False)
            action_message = "微信信息更新成功"

        # 情况5: 都不存在，创建新用户
        else:
            # 创建包含微信信息的用户数据
            from app.schemas.user import UserCreate

            wechat_info_dict = (
                wechat_user_info.dict()
                if hasattr(wechat_user_info, "dict")
                else wechat_user_info.__dict__
            )
            user_create_data = UserCreate(username=phone, login_type="wechat", **wechat_info_dict)
            user = await self.user_management.create_user_with_wechat_info(user_create_data, db)
            action_message = "注册成功"

        return user, action_message

    async def _update_user_wechat_info(
        self, user, openid: str, wechat_user_info: WeChatUserInfo, update_openid: bool = True
    ):
        """更新用户微信信息"""
        if update_openid:
            user.wechat_openid = openid
        user.wechat_unionid = wechat_user_info.unionid
        user.wechat_nickname = wechat_user_info.nickname
        user.wechat_avatar = wechat_user_info.headimgurl


# 全局服务实例将在 service_factory 中创建
