import pytest
from types import SimpleNamespace
from unittest.mock import AsyncMock

from app.crud.article import article as article_crud
from app.crud.user import user as user_crud
from app.crud.video import video as video_crud
from app.notifications.models import NotificationPriority, NotificationType
from app.tasks import notification_tasks


class DummyAsyncSession:
    def __init__(self):
        self.closed = False

    async def close(self):
        self.closed = True


@pytest.mark.asyncio
async def test_article_like_creates_notification(monkeypatch):
    db = DummyAsyncSession()
    monkeypatch.setattr("app.tasks.notification_tasks.SessionLocal", lambda: db)

    instances = []

    class FakeNotificationService:
        def __init__(self):
            self.create_notification = AsyncMock()
            instances.append(self)

    monkeypatch.setattr("app.tasks.notification_tasks.NotificationService", FakeNotificationService)
    monkeypatch.setattr(notification_tasks, "NotificationPriority", NotificationPriority, raising=False)
    monkeypatch.setattr(notification_tasks, "NotificationType", NotificationType, raising=False)

    article = SimpleNamespace(author_id=77, title="测试文章")
    liker = SimpleNamespace(username="alice")

    monkeypatch.setattr(article_crud, "get", AsyncMock(return_value=article))
    monkeypatch.setattr(user_crud, "get", AsyncMock(return_value=liker))

    await notification_tasks._create_like_notification_async(1, "article", 555)

    service = instances[0]
    service.create_notification.assert_awaited_once()
    kwargs = service.create_notification.await_args.kwargs

    assert kwargs["user_id"] == 77
    assert kwargs["title"] == "收到新点赞"
    assert kwargs["data"]["content_type"] == "article"
    assert kwargs["data"]["liker_username"] == "alice"
    assert kwargs["action_url"] == "/articles/555"
    assert db.closed is True


@pytest.mark.asyncio
async def test_video_like_uses_video_label_and_url(monkeypatch):
    db = DummyAsyncSession()
    monkeypatch.setattr("app.tasks.notification_tasks.SessionLocal", lambda: db)

    instances = []

    class FakeNotificationService:
        def __init__(self):
            self.create_notification = AsyncMock()
            instances.append(self)

    monkeypatch.setattr("app.tasks.notification_tasks.NotificationService", FakeNotificationService)
    monkeypatch.setattr(notification_tasks, "NotificationPriority", NotificationPriority, raising=False)
    monkeypatch.setattr(notification_tasks, "NotificationType", NotificationType, raising=False)

    video = SimpleNamespace(author_id=91, title="测试视频")
    liker = SimpleNamespace(username="bob")

    monkeypatch.setattr(video_crud, "get", AsyncMock(return_value=video))
    monkeypatch.setattr(user_crud, "get", AsyncMock(return_value=liker))

    await notification_tasks._create_like_notification_async(4, "video", 777)

    service = instances[0]
    service.create_notification.assert_awaited_once()
    kwargs = service.create_notification.await_args.kwargs

    assert kwargs["user_id"] == 91
    assert "视频" in kwargs["message"]
    assert kwargs["data"]["content_type"] == "video"
    assert kwargs["action_url"] == "/videos/777"
    assert db.closed is True


@pytest.mark.asyncio
async def test_like_by_author_skips_notification(monkeypatch):
    db = DummyAsyncSession()
    monkeypatch.setattr("app.tasks.notification_tasks.SessionLocal", lambda: db)

    service = AsyncMock()

    class FakeNotificationService:
        def __init__(self):
            self.create_notification = service

    monkeypatch.setattr("app.tasks.notification_tasks.NotificationService", FakeNotificationService)
    monkeypatch.setattr(notification_tasks, "NotificationPriority", NotificationPriority, raising=False)
    monkeypatch.setattr(notification_tasks, "NotificationType", NotificationType, raising=False)

    article = SimpleNamespace(author_id=5, title="自点赞文章")
    monkeypatch.setattr(article_crud, "get", AsyncMock(return_value=article))

    await notification_tasks._create_like_notification_async(5, "article", 888)

    service.assert_not_awaited()
    assert db.closed is True
