"""
Tests for MentionService
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from app.services.mention_service import MentionService, MentionValidationError
from app.models.user import User


@pytest.mark.asyncio
async def test_validate_mentions_success():
    """Test successful mention validation"""
    db = AsyncMock()
    
    # Mock users
    user1 = MagicMock()
    user1.id = 1
    user1.is_active = True
    user1.username = "user1"
    
    user2 = MagicMock()
    user2.id = 2
    user2.is_active = True
    user2.username = "user2"
    
    # Mock database query result
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = [user1, user2]
    db.execute.return_value = mock_result
    
    mention_ids = ["1", "2"]
    result = await MentionService.validate_mentions(db, mention_ids)
    
    assert len(result) == 2
    assert result[0].id == 1
    assert result[1].id == 2


@pytest.mark.asyncio
async def test_validate_mentions_empty_list():
    """Test validation with empty mention list"""
    db = AsyncMock()
    
    result = await MentionService.validate_mentions(db, [])
    
    assert result == []
    # Database should not be queried
    db.execute.assert_not_called()


@pytest.mark.asyncio
async def test_validate_mentions_too_many():
    """Test validation with too many mentions (>10)"""
    db = AsyncMock()
    
    mention_ids = [str(i) for i in range(1, 12)]  # 11 mentions
    
    with pytest.raises(MentionValidationError) as exc_info:
        await MentionService.validate_mentions(db, mention_ids)
    
    assert "最多只能提及10个用户" in str(exc_info.value)


@pytest.mark.asyncio
async def test_validate_mentions_invalid_id_format():
    """Test validation with invalid ID format"""
    db = AsyncMock()
    
    mention_ids = ["invalid", "not_a_number"]
    
    with pytest.raises(MentionValidationError) as exc_info:
        await MentionService.validate_mentions(db, mention_ids)
    
    assert "用户ID格式无效" in str(exc_info.value)


@pytest.mark.asyncio
async def test_validate_mentions_user_not_found():
    """Test validation when mentioned user doesn't exist"""
    db = AsyncMock()
    
    # Mock database query result - only one user found
    user1 = MagicMock()
    user1.id = 1
    user1.is_active = True
    
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = [user1]  # Only user 1, not user 999
    db.execute.return_value = mock_result
    
    mention_ids = ["1", "999"]  # User 999 doesn't exist
    
    with pytest.raises(MentionValidationError) as exc_info:
        await MentionService.validate_mentions(db, mention_ids)
    
    assert "用户不存在: 999" in str(exc_info.value)
    assert exc_info.value.user_id == "999"


@pytest.mark.asyncio
async def test_validate_mentions_inactive_user():
    """Test validation when mentioned user is inactive"""
    db = AsyncMock()
    
    # Mock users - one active, one inactive
    user1 = MagicMock()
    user1.id = 1
    user1.is_active = True
    user1.username = "user1"
    
    user2 = MagicMock()
    user2.id = 2
    user2.is_active = False  # Inactive user
    user2.username = "user2"
    
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = [user1, user2]
    db.execute.return_value = mock_result
    
    mention_ids = ["1", "2"]
    
    with pytest.raises(MentionValidationError) as exc_info:
        await MentionService.validate_mentions(db, mention_ids)
    
    assert "用户已停用: user2" in str(exc_info.value)
    assert exc_info.value.user_id == "2"


@pytest.mark.asyncio
async def test_validate_mentions_deduplication():
    """Test that duplicate mention IDs are deduplicated"""
    db = AsyncMock()
    
    user1 = MagicMock()
    user1.id = 1
    user1.is_active = True
    
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = [user1]
    db.execute.return_value = mock_result
    
    # Duplicate mention IDs
    mention_ids = ["1", "1", "1"]
    
    result = await MentionService.validate_mentions(db, mention_ids)
    
    assert len(result) == 1
    assert result[0].id == 1


def test_convert_users_to_mention_out():
    """Test converting users to mention output format"""
    user1 = MagicMock()
    user1.id = 1
    user1.username = "user1"
    user1.nickname = "User One"
    
    user2 = MagicMock()
    user2.id = 2
    user2.username = "user2"
    user2.nickname = None  # No nickname
    
    users = [user1, user2]
    
    result = MentionService.convert_users_to_mention_out(users)
    
    assert len(result) == 2
    
    # Check first user
    mention1 = result[0]
    assert mention1.id == "1"
    assert mention1.username == "user1"
    assert mention1.nickname == "User One"
    
    # Check second user (fallback to username when no nickname)
    mention2 = result[1]
    assert mention2.id == "2"
    assert mention2.username == "user2"
    assert mention2.nickname == "user2"


@pytest.mark.asyncio
async def test_create_mention_notifications():
    """Test creating mention notifications"""
    db = AsyncMock()
    
    # Mock mentioned users
    user1 = MagicMock()
    user1.id = 1
    
    user2 = MagicMock()
    user2.id = 2
    
    mentioned_users = [user1, user2]
    
    # Mock mentioner
    mentioner = MagicMock()
    mentioner.id = 3
    mentioner.username = "mentioner"
    mentioner.nickname = "The Mentioner"
    
    await MentionService.create_mention_notifications(
        db=db,
        mentioned_users=mentioned_users,
        mentioner=mentioner,
        content_type="comment",
        content_id=100,
        content_excerpt="Hello @user1 and @user2!"
    )
    
    # Verify database add was called for each mentioned user
    assert db.add.call_count == 2


@pytest.mark.asyncio
async def test_create_mention_notifications_skip_self():
    """Test that mention notifications are not created for self-mentions"""
    db = AsyncMock()
    
    # Mock mentioned user (same as mentioner)
    user1 = MagicMock()
    user1.id = 1
    
    mentioned_users = [user1]
    
    # Mock mentioner (same ID as mentioned user)
    mentioner = MagicMock()
    mentioner.id = 1  # Same as user1
    mentioner.username = "user1"
    
    await MentionService.create_mention_notifications(
        db=db,
        mentioned_users=mentioned_users,
        mentioner=mentioner,
        content_type="comment",
        content_id=100,
        content_excerpt="Hello @myself!"
    )
    
    # Verify no notifications were created (self-mention)
    db.add.assert_not_called()


@pytest.mark.asyncio
async def test_validate_mentions_whitespace_handling():
    """Test that whitespace in mention IDs is handled correctly"""
    db = AsyncMock()
    
    user1 = MagicMock()
    user1.id = 1
    user1.is_active = True
    
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = [user1]
    db.execute.return_value = mock_result
    
    # Mention IDs with whitespace
    mention_ids = [" 1 ", "  ", "1"]  # Should deduplicate to just [1]
    
    result = await MentionService.validate_mentions(db, mention_ids)
    
    assert len(result) == 1
    assert result[0].id == 1
