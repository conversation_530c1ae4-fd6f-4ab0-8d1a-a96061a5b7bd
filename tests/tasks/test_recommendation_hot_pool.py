"""手动触发热门推荐任务的测试。"""

from app.crud.user_behavior import HotContentItem
from app.tasks import recommendation_tasks
from tests.utils.test_helpers import MockRedis


class _DummySessionContext:
    """模拟SessionLocal的异步上下文管理器。"""

    def __init__(self, db):
        self._db = db

    async def __aenter__(self):
        return self._db

    async def __aexit__(self, exc_type, exc, tb):
        return False


def test_update_hot_recommendations_populates_hot_pool(monkeypatch):
    """确保更新热门推荐任务会写入Redis排序集合。"""
    mock_redis = MockRedis()

    async def fake_get_redis_master():
        return mock_redis

    async def fake_get_redis_slave():
        return mock_redis

    monkeypatch.setattr("app.db.redis.redis_master_client", mock_redis, raising=False)
    monkeypatch.setattr("app.db.redis.redis_slave_client", mock_redis, raising=False)
    monkeypatch.setattr("app.db.redis.get_redis_master", fake_get_redis_master)
    monkeypatch.setattr("app.db.redis.get_redis_slave", fake_get_redis_slave)

    mock_redis.sorted_sets["rec_pool:hot:article"] = {"0": 1.0}
    mock_redis.sorted_sets["rec_pool:hot:video"] = {"0": 2.0}

    dummy_db = object()

    def fake_session_local():
        return _DummySessionContext(dummy_db)

    monkeypatch.setattr("app.tasks.recommendation_tasks.SessionLocal", fake_session_local)

    async def fake_get_hot_content(db, *, content_type, limit=100, days=7):
        if content_type == "article":
            return [
                HotContentItem(content_id=101, hot_score=12.5),
                HotContentItem(content_id=102, hot_score=11.0),
            ]
        if content_type == "video":
            return [HotContentItem(content_id=201, hot_score=9.0)]
        return []

    monkeypatch.setattr(
        "app.tasks.recommendation_tasks.crud_user_behavior.get_hot_content",
        fake_get_hot_content,
    )

    recommendation_tasks.update_hot_recommendations()

    assert mock_redis.sorted_sets["rec_pool:hot:article"] == {
        "101": 12.5,
        "102": 11.0,
    }
    assert mock_redis.sorted_sets["rec_pool:hot:video"] == {"201": 9.0}
    assert "0" not in mock_redis.sorted_sets["rec_pool:hot:article"]
    assert "0" not in mock_redis.sorted_sets["rec_pool:hot:video"]
