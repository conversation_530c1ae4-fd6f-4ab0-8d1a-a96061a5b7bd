import types
from unittest.mock import AsyncMock, MagicMock

import pytest

from app import schemas
from app.services import user_management_service
from app.services.password_service import PasswordHashService
from app.services.user_management_service import UserManagementService


class DummyResult:
    def __init__(self, value):
        self._value = value

    def scalar_one_or_none(self):
        return self._value


class DummyDefaultRole:
    id = 1


class DummyAsyncSession:
    def __init__(self):
        self.added = []
        self.flush_called = False
        self.refreshed = []

    async def execute(self, stmt):  # noqa: ARG002 - stmt only for compatibility
        return DummyResult(DummyDefaultRole())

    async def flush(self):
        self.flush_called = True

    async def refresh(self, obj):
        self.refreshed.append(obj)

    async def commit(self):
        pass

    def add(self, obj):
        self.added.append(obj)


@pytest.mark.asyncio
async def test_create_user_initializes_default_folder(monkeypatch):
    password_service = MagicMock(spec=PasswordHashService)
    video_folder_service = types.SimpleNamespace(
        create_default_folder=AsyncMock(return_value=None)
    )

    service = UserManagementService(
        password_service=password_service,
        video_folder_service=video_folder_service,
    )

    dummy_user = types.SimpleNamespace(id=42, avatar=None)

    monkeypatch.setattr(
        user_management_service.crud.user,
        "create",
        AsyncMock(return_value=dummy_user),
    )
    monkeypatch.setattr(
        user_management_service.user_stats,
        "get_or_create",
        AsyncMock(return_value=None),
    )

    db = DummyAsyncSession()
    user_create = schemas.UserCreate(username="17604840254")

    result = await service._create_user_with_defaults(db, obj_in=user_create)

    assert result is dummy_user
    video_folder_service.create_default_folder.assert_awaited_once()
    call_args = video_folder_service.create_default_folder.await_args
    assert call_args.args[0] is db
    assert call_args.kwargs["user_id"] == dummy_user.id
    assert call_args.kwargs["commit"] is False
