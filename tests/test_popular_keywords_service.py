"""热门搜索词服务测试"""

from datetime import datetime, timezone

import pytest

import app.services.search_suggestion_service as search_suggestion_service
from app.services.search_suggestion_service import PopularKeywordsService


@pytest.mark.asyncio
async def test_popular_keywords_service_returns_aggregated_data(monkeypatch):
    """验证服务在数据库返回数据时的行为"""

    async def fake_get_popular_keywords(*args, **kwargs):
        return [
            {
                "keyword": "Python",
                "content_type": "article",
                "search_count": 120,
                "last_searched_at": datetime(2025, 10, 29, tzinfo=timezone.utc),
                "updated_at": datetime(2025, 10, 29, tzinfo=timezone.utc),
            },
            {
                "keyword": "Vue",
                "content_type": "video",
                "search_count": 95,
                "last_searched_at": None,
                "updated_at": None,
            },
        ]

    monkeypatch.setattr(
        search_suggestion_service.search_stats,
        "get_popular_keywords",
        fake_get_popular_keywords,
    )

    result = await PopularKeywordsService._load_popular_keywords(
        db=None,
        content_type="all",
        period="day",
        limit=5,
    )

    assert len(result) == 2
    assert result[0].keyword == "Python"
    assert result[0].rank == 1
    assert result[0].search_count == 120
    assert result[0].content_type == "article"
    assert result[0].source == "aggregated"
    assert result[1].rank == 2
    assert result[1].keyword == "Vue"
    assert result[1].source == "aggregated"


@pytest.mark.asyncio
async def test_popular_keywords_service_fallback(monkeypatch):
    """当数据库查询失败时，服务应使用 fallback 数据"""

    async def fake_get_popular_keywords(*args, **kwargs):  # pragma: no cover - 模拟异常分支
        raise RuntimeError("db unavailable")

    monkeypatch.setattr(
        search_suggestion_service.search_stats,
        "get_popular_keywords",
        fake_get_popular_keywords,
    )

    result = await PopularKeywordsService._load_popular_keywords(
        db=None,
        content_type="all",
        period="day",
        limit=3,
    )

    assert len(result) == 3
    assert all(item.source == "fallback" for item in result)
    assert [item.rank for item in result] == [1, 2, 3]
    assert result[0].search_count > result[1].search_count
