"""
提及功能相关服务

提供用户提及的验证、通知发送等功能
"""

from typing import Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.models.user import User
from app.schemas.comment import CommentMentionOut


class MentionValidationError(Exception):
    """提及验证错误"""
    
    def __init__(self, message: str, user_id: str | None = None):
        self.message = message
        self.user_id = user_id
        super().__init__(message)


class MentionService:
    """提及服务"""
    
    @staticmethod
    async def validate_mentions(
        db: AsyncSession,
        mention_ids: list[str],
        current_user: User | None = None
    ) -> list[User]:
        """
        验证提及的用户ID列表
        
        Args:
            db: 数据库会话
            mention_ids: 提及的用户ID列表
            current_user: 当前用户（用于权限检查）
            
        Returns:
            验证通过的用户列表
            
        Raises:
            MentionValidationError: 验证失败时抛出
        """
        if not mention_ids:
            return []
        
        # 限制最多10个提及
        if len(mention_ids) > 10:
            raise MentionValidationError("最多只能提及10个用户")
        
        # 转换为整数ID并去重
        try:
            unique_ids = list(dict.fromkeys([int(mid) for mid in mention_ids if mid.strip()]))
        except ValueError as e:
            raise MentionValidationError("用户ID格式无效") from e
        
        if not unique_ids:
            return []
        
        # 查询用户
        query = select(User).where(User.id.in_(unique_ids))
        result = await db.execute(query)
        users = result.scalars().all()
        
        # 检查所有用户是否存在
        found_ids = {user.id for user in users}
        missing_ids = set(unique_ids) - found_ids
        if missing_ids:
            raise MentionValidationError(
                f"用户不存在: {', '.join(map(str, missing_ids))}",
                user_id=str(list(missing_ids)[0])
            )
        
        # 检查用户是否激活
        inactive_users = [user for user in users if not user.is_active]
        if inactive_users:
            raise MentionValidationError(
                f"用户已停用: {inactive_users[0].username}",
                user_id=str(inactive_users[0].id)
            )
        
        # TODO: 添加更多权限检查
        # - 检查是否被屏蔽
        # - 检查是否屏蔽了当前用户
        # - 检查是否有提及权限
        
        return users
    
    @staticmethod
    def convert_users_to_mention_out(users: list[User]) -> list[CommentMentionOut]:
        """
        将用户列表转换为提及输出格式
        
        Args:
            users: 用户列表
            
        Returns:
            提及输出列表
        """
        return [
            CommentMentionOut(
                id=str(user.id),
                username=user.username,
                nickname=user.nickname or user.username
            )
            for user in users
        ]
    
    @staticmethod
    async def create_mention_notifications(
        db: AsyncSession,
        mentioned_users: list[User],
        mentioner: User,
        content_type: str,
        content_id: int,
        content_excerpt: str
    ) -> None:
        """
        为被提及的用户创建通知
        
        Args:
            db: 数据库会话
            mentioned_users: 被提及的用户列表
            mentioner: 提及者
            content_type: 内容类型 (comment, post)
            content_id: 内容ID
            content_excerpt: 内容摘要
        """
        try:
            from app.models.outbox import OutboxMessage
            
            for mentioned_user in mentioned_users:
                # 不向自己发送提及通知
                if mentioned_user.id == mentioner.id:
                    continue
                
                # 创建提及通知事件
                notification_event = OutboxMessage(
                    topic="mention.created",
                    payload={
                        "mentioned_user_id": mentioned_user.id,
                        "mentioner_id": mentioner.id,
                        "mentioner_name": mentioner.nickname or mentioner.username,
                        "content_type": content_type,
                        "content_id": content_id,
                        "content_excerpt": content_excerpt[:200],  # 限制摘要长度
                    }
                )
                db.add(notification_event)
                
            logger.info(
                f"创建了 {len([u for u in mentioned_users if u.id != mentioner.id])} 个提及通知"
            )
            
        except Exception as e:
            logger.error(f"创建提及通知失败: {str(e)}")
            # 不抛出异常，避免影响主要功能
