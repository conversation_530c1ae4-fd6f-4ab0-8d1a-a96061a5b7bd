"""
Tests for comment schemas with mentions functionality
"""

import pytest
from pydantic import ValidationError

from app.schemas.comment import CommentBase, CommentCreate, CommentUpdate, CommentMentionOut
from app.models.comment import CommentType


def test_comment_base_with_mentions():
    """Test CommentBase schema with mentions field"""
    data = {
        "content": "Hello @user1 and @user2!",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": ["1", "2"]
    }

    comment = CommentBase(**data)

    assert comment.content == "Hello @user1 and @user2!"
    assert comment.mentions == ["1", "2"]


def test_comment_base_without_mentions():
    """Test CommentBase schema without mentions field (should default to empty list)"""
    data = {
        "content": "Hello world!",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1
    }

    comment = CommentBase(**data)

    assert comment.content == "Hello world!"
    assert comment.mentions == []


def test_comment_create_with_mentions():
    """Test CommentCreate schema with mentions"""
    data = {
        "content": "Test comment with mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": ["1", "2", "3"]
    }
    
    comment = CommentCreate(**data)
    
    assert comment.content == "Test comment with mentions"
    assert comment.comment_type == CommentType.ARTICLE
    assert comment.article_id == 1
    assert comment.mentions == ["1", "2", "3"]


def test_comment_update_with_mentions():
    """Test CommentUpdate schema with mentions"""
    data = {
        "content": "Updated comment",
        "mentions": ["4", "5"]
    }
    
    comment = CommentUpdate(**data)
    
    assert comment.content == "Updated comment"
    assert comment.mentions == ["4", "5"]


def test_mentions_validation_too_many():
    """Test that mentions validation rejects more than 10 mentions"""
    data = {
        "content": "Too many mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": [str(i) for i in range(1, 12)]  # 11 mentions
    }

    with pytest.raises(ValidationError) as exc_info:
        CommentBase(**data)

    errors = exc_info.value.errors()
    # Should have error for mentions validation
    mentions_error = next((e for e in errors if e["loc"] == ("mentions",)), None)
    assert mentions_error is not None
    assert "最多只能提及10个用户" in mentions_error["msg"]


def test_mentions_validation_deduplication():
    """Test that duplicate mentions are removed"""
    data = {
        "content": "Duplicate mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": ["1", "2", "1", "3", "2"]  # Duplicates
    }

    comment = CommentBase(**data)

    # Should be deduplicated to ["1", "2", "3"]
    assert len(comment.mentions) == 3
    assert "1" in comment.mentions
    assert "2" in comment.mentions
    assert "3" in comment.mentions


def test_mentions_validation_empty_strings():
    """Test that empty strings in mentions are filtered out"""
    data = {
        "content": "Empty mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": ["1", "", "2", "   ", "3"]  # Empty and whitespace strings
    }

    comment = CommentBase(**data)

    # Should filter out empty strings
    assert comment.mentions == ["1", "2", "3"]


def test_mentions_validation_none_input():
    """Test that None input for mentions defaults to empty list"""
    data = {
        "content": "No mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": None
    }

    comment = CommentBase(**data)

    assert comment.mentions == []


def test_comment_mention_out_schema():
    """Test CommentMentionOut schema"""
    data = {
        "id": "123",
        "username": "testuser",
        "nickname": "Test User"
    }
    
    mention = CommentMentionOut(**data)
    
    assert mention.id == "123"
    assert mention.username == "testuser"
    assert mention.nickname == "Test User"


def test_comment_mention_out_schema_no_nickname():
    """Test CommentMentionOut schema with nickname (required field)"""
    data = {
        "id": "123",
        "username": "testuser",
        "nickname": "Test User"
    }

    mention = CommentMentionOut(**data)

    assert mention.id == "123"
    assert mention.username == "testuser"
    assert mention.nickname == "Test User"


def test_mentions_validation_whitespace_trimming():
    """Test that whitespace in mention IDs is trimmed"""
    data = {
        "content": "Whitespace mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": [" 1 ", "  2  ", "3"]
    }

    comment = CommentBase(**data)

    # Should trim whitespace
    assert comment.mentions == ["1", "2", "3"]


def test_mentions_validation_mixed_valid_invalid():
    """Test mentions validation with mix of valid and invalid IDs"""
    data = {
        "content": "Mixed mentions",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": ["1", "", "2", "   ", "3", ""]
    }

    comment = CommentBase(**data)

    # Should keep only valid non-empty IDs
    assert comment.mentions == ["1", "2", "3"]


def test_comment_create_article_with_mentions():
    """Test creating article comment with mentions"""
    data = {
        "content": "Article comment with @user1",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "mentions": ["1"]
    }
    
    comment = CommentCreate(**data)
    
    assert comment.comment_type == CommentType.ARTICLE
    assert comment.article_id == 1
    assert comment.video_id is None
    assert comment.mentions == ["1"]


def test_comment_create_video_with_mentions():
    """Test creating video comment with mentions"""
    data = {
        "content": "Video comment with @user1",
        "comment_type": CommentType.VIDEO,
        "video_id": 1,
        "mentions": ["1"]
    }
    
    comment = CommentCreate(**data)
    
    assert comment.comment_type == CommentType.VIDEO
    assert comment.video_id == 1
    assert comment.article_id is None
    assert comment.mentions == ["1"]


def test_comment_create_reply_with_mentions():
    """Test creating reply comment with mentions"""
    data = {
        "content": "Reply with @user1",
        "comment_type": CommentType.ARTICLE,
        "article_id": 1,
        "parent_id": 10,
        "mentions": ["1"]
    }
    
    comment = CommentCreate(**data)
    
    assert comment.comment_type == CommentType.ARTICLE
    assert comment.article_id == 1
    assert comment.parent_id == 10
    assert comment.mentions == ["1"]


def test_mentions_field_description():
    """Test that mentions field has proper description"""
    schema = CommentBase.model_json_schema()
    
    mentions_field = schema["properties"]["mentions"]
    assert "被提及的用户ID列表" in mentions_field["description"]
    assert "最多10个" in mentions_field["description"]


def test_mentions_field_default():
    """Test that mentions field has proper default value"""
    schema = CommentBase.model_json_schema()

    mentions_field = schema["properties"]["mentions"]
    # Check that the field has a default factory (empty list)
    assert "default" in mentions_field or mentions_field.get("type") == "array"
