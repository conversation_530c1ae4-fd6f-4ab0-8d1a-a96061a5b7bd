"""Add mentions field to comments table

Revision ID: bf1d7dfea244
Revises: f2c8b4e2c1df
Create Date: 2025-11-04 01:22:25.778064

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bf1d7dfea244'
down_revision: Union[str, None] = 'f2c8b4e2c1df'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('idx_posts_author_created'), table_name='posts')
    op.drop_index(op.f('idx_posts_hot_score'), table_name='posts')
    op.drop_index(op.f('idx_posts_status_created'), table_name='posts')
    op.drop_index(op.f('idx_posts_topic'), table_name='posts')
    op.drop_index(op.f('idx_posts_type_created'), table_name='posts')
    op.drop_index(op.f('idx_posts_visibility_status'), table_name='posts')
    op.drop_index(op.f('ix_posts_id'), table_name='posts')
    op.drop_table('posts')
    op.drop_index(op.f('idx_search_history_created'), table_name='search_history')
    op.drop_index(op.f('idx_search_history_query'), table_name='search_history')
    op.drop_index(op.f('idx_search_history_user'), table_name='search_history')
    op.drop_index(op.f('ix_search_history_id'), table_name='search_history')
    op.drop_index(op.f('ix_search_history_user_id'), table_name='search_history')
    op.drop_table('search_history')
    op.drop_index(op.f('ix_admin_feature_toggles_is_enabled'), table_name='admin_feature_toggles')
    op.drop_index(op.f('ix_admin_feature_toggles_scope'), table_name='admin_feature_toggles')
    op.drop_table('admin_feature_toggles')
    op.drop_table('scratch_project_tags')
    op.drop_index(op.f('ix_outbox_messages_id'), table_name='outbox_messages')
    op.drop_index(op.f('ix_outbox_messages_status'), table_name='outbox_messages')
    op.drop_index(op.f('ix_outbox_messages_topic'), table_name='outbox_messages')
    op.drop_table('outbox_messages')
    op.drop_index(op.f('idx_post_media_post_id'), table_name='post_media')
    op.drop_index(op.f('idx_post_media_type'), table_name='post_media')
    op.drop_index(op.f('ix_post_media_id'), table_name='post_media')
    op.drop_table('post_media')
    op.drop_index(op.f('ix_admin_config_change_logs_category_changed_at'), table_name='admin_config_change_logs')
    op.drop_index(op.f('ix_admin_config_change_logs_changed_at'), table_name='admin_config_change_logs')
    op.drop_index(op.f('ix_admin_config_change_logs_changed_by'), table_name='admin_config_change_logs')
    op.drop_index(op.f('ix_admin_config_change_logs_config_key'), table_name='admin_config_change_logs')
    op.drop_index(op.f('ix_admin_config_change_logs_rollback_id'), table_name='admin_config_change_logs')
    op.drop_table('admin_config_change_logs')
    op.drop_index(op.f('ix_tag_stats_content_type'), table_name='tag_stats')
    op.drop_index(op.f('ix_tag_stats_id'), table_name='tag_stats')
    op.drop_index(op.f('ix_tag_stats_tag_id'), table_name='tag_stats')
    op.drop_table('tag_stats')
    op.drop_index(op.f('idx_scratch_adapt_level'), table_name='scratch_products')
    op.drop_index(op.f('idx_scratch_adaptation_type'), table_name='scratch_products')
    op.drop_index(op.f('idx_scratch_original_project'), table_name='scratch_products')
    op.drop_index(op.f('idx_scratch_products_search_vector'), table_name='scratch_products', postgresql_using='gin')
    op.drop_index(op.f('idx_scratch_products_title_trgm'), table_name='scratch_products', postgresql_using='gist')
    op.drop_index(op.f('idx_scratch_root_project'), table_name='scratch_products')
    op.drop_index(op.f('ix_scratch_author_id'), table_name='scratch_products')
    op.drop_index(op.f('ix_scratch_is_published'), table_name='scratch_products')
    op.drop_index(op.f('ix_scratch_title'), table_name='scratch_products')
    op.drop_table('scratch_products')
    op.drop_index(op.f('idx_post_mentions_post_id'), table_name='post_mentions')
    op.drop_index(op.f('idx_post_mentions_user_id'), table_name='post_mentions')
    op.drop_index(op.f('ix_post_mentions_id'), table_name='post_mentions')
    op.drop_table('post_mentions')
    op.drop_index(op.f('ix_admin_config_settings_category'), table_name='admin_config_settings')
    op.drop_index(op.f('ix_admin_config_settings_category_key'), table_name='admin_config_settings')
    op.drop_index(op.f('ix_admin_config_settings_is_sensitive'), table_name='admin_config_settings')
    op.drop_table('admin_config_settings')
    op.drop_index(op.f('ix_notifications_created_at'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_priority'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_status'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_type'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_user_id'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_user_status'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_admin_analytics_snapshot_data_created_at'), table_name='admin_analytics_snapshot_data')
    op.drop_index(op.f('ix_admin_analytics_snapshot_data_data_type'), table_name='admin_analytics_snapshot_data')
    op.drop_index(op.f('ix_admin_analytics_snapshot_data_snapshot_id'), table_name='admin_analytics_snapshot_data')
    op.drop_table('admin_analytics_snapshot_data')
    op.drop_index(op.f('ix_backpack_items_user_id'), table_name='backpack_items')
    op.drop_index(op.f('ix_backpack_items_user_id_type'), table_name='backpack_items')
    op.drop_table('backpack_items')
    op.drop_index(op.f('ix_user_interests_id'), table_name='user_interests')
    op.drop_index(op.f('ix_user_interests_interest_tag'), table_name='user_interests')
    op.drop_index(op.f('ix_user_interests_user_id'), table_name='user_interests')
    op.drop_table('user_interests')
    op.drop_index(op.f('ix_admin_alert_events_notification_status'), table_name='admin_alert_events')
    op.drop_index(op.f('ix_admin_alert_events_rule_id'), table_name='admin_alert_events')
    op.drop_index(op.f('ix_admin_alert_events_status'), table_name='admin_alert_events')
    op.drop_index(op.f('ix_admin_alert_events_triggered_at'), table_name='admin_alert_events')
    op.drop_table('admin_alert_events')
    op.drop_index(op.f('idx_post_poll_votes_post_id'), table_name='post_poll_votes')
    op.drop_index(op.f('idx_post_poll_votes_unique'), table_name='post_poll_votes')
    op.drop_index(op.f('idx_post_poll_votes_user_id'), table_name='post_poll_votes')
    op.drop_index(op.f('ix_post_poll_votes_id'), table_name='post_poll_votes')
    op.drop_table('post_poll_votes')
    op.drop_index(op.f('idx_search_suggestions_keyword'), table_name='search_suggestions')
    op.drop_index(op.f('idx_search_suggestions_weight'), table_name='search_suggestions')
    op.drop_index(op.f('ix_search_suggestions_id'), table_name='search_suggestions')
    op.drop_table('search_suggestions')
    op.drop_index(op.f('ix_admin_alert_rules_created_at'), table_name='admin_alert_rules')
    op.drop_index(op.f('ix_admin_alert_rules_enabled'), table_name='admin_alert_rules')
    op.drop_index(op.f('ix_admin_alert_rules_metric_name'), table_name='admin_alert_rules')
    op.drop_index(op.f('ix_admin_alert_rules_severity'), table_name='admin_alert_rules')
    op.drop_table('admin_alert_rules')
    op.drop_index(op.f('idx_topic_stats_hot_score'), table_name='topic_stats')
    op.drop_index(op.f('idx_topic_stats_last_post_at'), table_name='topic_stats')
    op.drop_index(op.f('idx_topic_stats_post_count'), table_name='topic_stats')
    op.drop_index(op.f('idx_topic_stats_trend_score'), table_name='topic_stats')
    op.drop_index(op.f('idx_topic_stats_updated_at'), table_name='topic_stats')
    op.drop_table('topic_stats')
    op.drop_index(op.f('ix_audit_logs_action'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_action_timestamp'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_request_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_resource_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_resource_timestamp'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_resource_type'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_timestamp'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_user_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_user_timestamp'), table_name='audit_logs')
    op.drop_table('audit_logs')
    op.drop_index(op.f('ix_admin_monitoring_metrics_metric_name'), table_name='admin_monitoring_metrics')
    op.drop_index(op.f('ix_admin_monitoring_metrics_metric_timestamp'), table_name='admin_monitoring_metrics')
    op.drop_index(op.f('ix_admin_monitoring_metrics_source'), table_name='admin_monitoring_metrics')
    op.drop_index(op.f('ix_admin_monitoring_metrics_timestamp'), table_name='admin_monitoring_metrics')
    op.drop_table('admin_monitoring_metrics')
    op.drop_index(op.f('ix_admin_analytics_snapshots_generated_at'), table_name='admin_analytics_snapshots')
    op.drop_index(op.f('ix_admin_analytics_snapshots_status'), table_name='admin_analytics_snapshots')
    op.drop_index(op.f('ix_admin_analytics_snapshots_time_range'), table_name='admin_analytics_snapshots')
    op.drop_index(op.f('ix_admin_analytics_snapshots_type'), table_name='admin_analytics_snapshots')
    op.drop_table('admin_analytics_snapshots')
    op.drop_index(op.f('idx_search_keywords_stats_count'), table_name='search_keywords_stats')
    op.drop_index(op.f('idx_search_keywords_stats_date'), table_name='search_keywords_stats')
    op.drop_index(op.f('idx_search_keywords_stats_keyword'), table_name='search_keywords_stats')
    op.drop_index(op.f('ix_search_keywords_stats_id'), table_name='search_keywords_stats')
    op.drop_table('search_keywords_stats')
    op.drop_table('user_stats')
    op.alter_column('articles', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('articles', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('idx_articles_search_vector'), table_name='articles', postgresql_using='gin')
    op.drop_index(op.f('idx_articles_title_trgm'), table_name='articles', postgresql_using='gist')
    op.drop_index(op.f('ix_articles_author_published_approved_deleted_updated_at'), table_name='articles')
    op.drop_index(op.f('ix_articles_category_published_approved_deleted_updated_at'), table_name='articles', postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.drop_index(op.f('ix_articles_is_deleted'), table_name='articles')
    op.drop_index(op.f('ix_articles_slug'), table_name='articles')
    op.drop_index(op.f('ix_articles_status_updated_at'), table_name='articles', postgresql_where='(is_deleted = false)')
    op.drop_index(op.f('ix_articles_title_content_gin'), table_name='articles', postgresql_using='gin', postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.drop_index(op.f('ix_articles_visit_count_created_at'), table_name='articles', postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.drop_column('articles', 'is_deleted')
    op.drop_column('articles', 'cache_version')
    op.drop_column('articles', 'slug')
    op.drop_column('articles', 'search_vector')
    op.alter_column('banners', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('banners', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('banners', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('categories', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('categories', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('comments', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('comments', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_comments_article_created_at'), table_name='comments', postgresql_where='((article_id IS NOT NULL) AND (is_visible = true))')
    op.drop_index(op.f('ix_comments_post_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_scratch_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_video_created_at'), table_name='comments', postgresql_where='((video_id IS NOT NULL) AND (is_visible = true))')
    op.drop_constraint(op.f('comments_scratch_id_fkey'), 'comments', type_='foreignkey')
    op.drop_constraint(op.f('comments_post_id_fkey'), 'comments', type_='foreignkey')
    op.drop_column('comments', 'scratch_id')
    op.drop_column('comments', 'post_id')
    op.drop_column('comments', 'image_urls')
    op.alter_column('content_similarities', 'calculated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('favorites', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('favorites', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_favorites_content_active_count'), table_name='favorites', postgresql_where='(is_active = true)')
    op.drop_index(op.f('ix_favorites_user_content_active_created_at'), table_name='favorites', postgresql_where='(is_active = true)')
    op.alter_column('file_hashes', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('file_hashes', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('history', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('history', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('history', 'last_visited_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_history_user_content_updated_at'), table_name='history')
    op.alter_column('likes', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('likes', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_likes_content_active_count'), table_name='likes', postgresql_where='(is_active = true)')
    op.drop_index(op.f('ix_likes_user_content_active_created_at'), table_name='likes', postgresql_where='(is_active = true)')
    op.alter_column('permission', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('permission', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('recommendation_logs', 'clicked_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('recommendation_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('reviews', 'reviewed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('reviews', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('reviews', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('tags', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('tags', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_tags_content_type'), table_name='tags')
    op.drop_column('tags', 'content_type')
    op.alter_column('user_browse_history', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('user_devices', 'last_login_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_comment='最后登录时间',
               existing_nullable=False)
    op.alter_column('user_devices', 'first_login_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_comment='首次登录时间',
               existing_nullable=False)
    op.alter_column('user_devices', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('user_devices', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_user_devices_last_login_at'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_trust_score'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_user_id_active_blocked'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_user_id_fingerprint'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_user_id_trusted'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_user_trusted_updated_at'), table_name='user_devices')
    op.alter_column('user_interactions', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('ix_user_interactions_created_at_user_id'), table_name='user_interactions')
    op.alter_column('user_profiles', 'last_updated',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('user_profiles', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('user_role', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('user_role', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('users', 'last_login',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('users', 'wechat_nickname',
               existing_type=sa.VARCHAR(length=100),
               comment='微信昵称',
               existing_nullable=True)
    op.alter_column('users', 'wechat_avatar',
               existing_type=sa.VARCHAR(length=255),
               comment='微信头像',
               existing_nullable=True)
    op.drop_index(op.f('ix_users_is_deleted'), table_name='users')
    op.drop_column('users', 'is_deleted')
    op.drop_column('users', 'cache_version')
    op.drop_column('users', 'likes_privacy_settings')
    op.drop_column('users', 'favorites_privacy_settings')
    op.alter_column('video_folders', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('video_folders', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('video_folders', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('videos', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('videos', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('videos', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index(op.f('idx_videos_search_vector'), table_name='videos', postgresql_using='gin')
    op.drop_index(op.f('idx_videos_title_trgm'), table_name='videos', postgresql_using='gist')
    op.drop_index(op.f('ix_videos_author_published_approved_deleted_updated_at'), table_name='videos')
    op.drop_index(op.f('ix_videos_folder_published_approved_deleted_updated_at'), table_name='videos', postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.drop_index(op.f('ix_videos_slug'), table_name='videos')
    op.drop_column('videos', 'cache_version')
    op.drop_column('videos', 'slug')
    op.drop_column('videos', 'search_vector')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('videos', sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True, comment='全文搜索向量'))
    op.add_column('videos', sa.Column('slug', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='视频别名'))
    op.add_column('videos', sa.Column('cache_version', sa.INTEGER(), server_default=sa.text('1'), autoincrement=False, nullable=False, comment='缓存版本号'))
    op.create_index(op.f('ix_videos_slug'), 'videos', ['slug'], unique=True)
    op.create_index(op.f('ix_videos_folder_published_approved_deleted_updated_at'), 'videos', ['folder_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at'], unique=False, postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.create_index(op.f('ix_videos_author_published_approved_deleted_updated_at'), 'videos', ['author_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at'], unique=False)
    op.create_index(op.f('idx_videos_title_trgm'), 'videos', ['title'], unique=False, postgresql_using='gist')
    op.create_index(op.f('idx_videos_search_vector'), 'videos', ['search_vector'], unique=False, postgresql_using='gin')
    op.alter_column('videos', 'deleted_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('videos', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('videos', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'deleted_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.add_column('users', sa.Column('favorites_privacy_settings', sa.INTEGER(), server_default=sa.text('3'), autoincrement=False, nullable=False, comment='收藏隐私设置 (位掩码: 1=文章, 2=视频)'))
    op.add_column('users', sa.Column('likes_privacy_settings', sa.INTEGER(), server_default=sa.text('3'), autoincrement=False, nullable=False, comment='点赞隐私设置 (位掩码: 1=文章, 2=视频)'))
    op.add_column('users', sa.Column('cache_version', sa.INTEGER(), server_default=sa.text('1'), autoincrement=False, nullable=False, comment='缓存版本号'))
    op.add_column('users', sa.Column('is_deleted', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False))
    op.create_index(op.f('ix_users_is_deleted'), 'users', ['is_deleted'], unique=False)
    op.alter_column('users', 'wechat_avatar',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='微信头像',
               existing_nullable=True)
    op.alter_column('users', 'wechat_nickname',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='微信昵称',
               existing_nullable=True)
    op.alter_column('users', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('users', 'last_login',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_role', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_role', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_profiles', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_profiles', 'last_updated',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_index(op.f('ix_user_interactions_created_at_user_id'), 'user_interactions', ['created_at', 'user_id'], unique=False)
    op.alter_column('user_interactions', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_index(op.f('ix_user_devices_user_trusted_updated_at'), 'user_devices', ['user_id', 'is_trusted', 'updated_at'], unique=False)
    op.create_index(op.f('ix_user_devices_user_id_trusted'), 'user_devices', ['user_id', 'is_trusted'], unique=False)
    op.create_index(op.f('ix_user_devices_user_id_fingerprint'), 'user_devices', ['user_id', 'device_fingerprint'], unique=False)
    op.create_index(op.f('ix_user_devices_user_id_active_blocked'), 'user_devices', ['user_id', 'is_active', 'is_blocked'], unique=False)
    op.create_index(op.f('ix_user_devices_trust_score'), 'user_devices', ['trust_score'], unique=False)
    op.create_index(op.f('ix_user_devices_last_login_at'), 'user_devices', ['last_login_at'], unique=False)
    op.alter_column('user_devices', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'first_login_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_comment='首次登录时间',
               existing_nullable=False)
    op.alter_column('user_devices', 'last_login_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_comment='最后登录时间',
               existing_nullable=False)
    op.alter_column('user_browse_history', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.add_column('tags', sa.Column('content_type', sa.VARCHAR(length=32), autoincrement=False, nullable=True))
    op.create_index(op.f('ix_tags_content_type'), 'tags', ['content_type'], unique=False)
    op.alter_column('tags', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('tags', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'reviewed_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('recommendation_logs', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('recommendation_logs', 'clicked_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('permission', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('permission', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_index(op.f('ix_likes_user_content_active_created_at'), 'likes', ['user_id', 'content_type', 'is_active', 'created_at'], unique=False, postgresql_where='(is_active = true)')
    op.create_index(op.f('ix_likes_content_active_count'), 'likes', ['content_id', 'content_type', 'is_active'], unique=False, postgresql_where='(is_active = true)')
    op.alter_column('likes', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('likes', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_index(op.f('ix_history_user_content_updated_at'), 'history', ['user_id', 'content_type', 'updated_at'], unique=False)
    op.alter_column('history', 'last_visited_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('history', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('history', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('file_hashes', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('file_hashes', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_index(op.f('ix_favorites_user_content_active_created_at'), 'favorites', ['user_id', 'content_type', 'is_active', 'created_at'], unique=False, postgresql_where='(is_active = true)')
    op.create_index(op.f('ix_favorites_content_active_count'), 'favorites', ['content_id', 'content_type', 'is_active'], unique=False, postgresql_where='(is_active = true)')
    op.alter_column('favorites', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('favorites', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('content_similarities', 'calculated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.add_column('comments', sa.Column('image_urls', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False))
    op.add_column('comments', sa.Column('post_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('comments', sa.Column('scratch_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f('comments_post_id_fkey'), 'comments', 'posts', ['post_id'], ['id'])
    op.create_foreign_key(op.f('comments_scratch_id_fkey'), 'comments', 'scratch_products', ['scratch_id'], ['project_id'], ondelete='SET NULL')
    op.create_index(op.f('ix_comments_video_created_at'), 'comments', ['video_id', 'created_at'], unique=False, postgresql_where='((video_id IS NOT NULL) AND (is_visible = true))')
    op.create_index(op.f('ix_comments_scratch_id'), 'comments', ['scratch_id'], unique=False)
    op.create_index(op.f('ix_comments_post_id'), 'comments', ['post_id'], unique=False)
    op.create_index(op.f('ix_comments_article_created_at'), 'comments', ['article_id', 'created_at'], unique=False, postgresql_where='((article_id IS NOT NULL) AND (is_visible = true))')
    op.alter_column('comments', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('comments', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('categories', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('categories', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'deleted_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.add_column('articles', sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True, comment='全文搜索向量'))
    op.add_column('articles', sa.Column('slug', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('articles', sa.Column('cache_version', sa.INTEGER(), server_default=sa.text('1'), autoincrement=False, nullable=False, comment='缓存版本号'))
    op.add_column('articles', sa.Column('is_deleted', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False))
    op.create_index(op.f('ix_articles_visit_count_created_at'), 'articles', ['visit_count', 'created_at'], unique=False, postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.create_index(op.f('ix_articles_title_content_gin'), 'articles', [sa.literal_column("to_tsvector('chinese'::regconfig, (COALESCE(title, ''::character varying)::text || ' '::text) || COALESCE(content, ''::text))")], unique=False, postgresql_using='gin', postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.create_index(op.f('ix_articles_status_updated_at'), 'articles', ['is_published', 'is_approved', 'is_deleted', 'updated_at'], unique=False, postgresql_where='(is_deleted = false)')
    op.create_index(op.f('ix_articles_slug'), 'articles', ['slug'], unique=False)
    op.create_index(op.f('ix_articles_is_deleted'), 'articles', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_articles_category_published_approved_deleted_updated_at'), 'articles', ['category_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at'], unique=False, postgresql_where='((is_published = true) AND (is_approved = true) AND (is_deleted = false))')
    op.create_index(op.f('ix_articles_author_published_approved_deleted_updated_at'), 'articles', ['author_id', 'is_published', 'is_approved', 'is_deleted', 'updated_at'], unique=False)
    op.create_index(op.f('idx_articles_title_trgm'), 'articles', ['title'], unique=False, postgresql_using='gist')
    op.create_index(op.f('idx_articles_search_vector'), 'articles', ['search_vector'], unique=False, postgresql_using='gin')
    op.alter_column('articles', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('articles', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.create_table('user_stats',
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('total_likes_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('total_favorites_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('following_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('follower_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('article_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('video_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('user_stats_user_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('user_id', name=op.f('user_stats_pkey'))
    )
    op.create_table('search_keywords_stats',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('keyword', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='搜索关键词'),
    sa.Column('content_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False, comment='内容类型'),
    sa.Column('search_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='搜索次数'),
    sa.Column('result_count_avg', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True, comment='平均搜索结果数'),
    sa.Column('click_through_rate', sa.NUMERIC(precision=5, scale=4), autoincrement=False, nullable=True, comment='点击率'),
    sa.Column('last_searched_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='最后搜索时间'),
    sa.Column('stat_date', sa.DATE(), autoincrement=False, nullable=False, comment='统计日期'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.CheckConstraint("content_type::text = ANY (ARRAY['article'::character varying, 'video'::character varying, 'scratch'::character varying, 'all'::character varying]::text[])", name=op.f('check_keywords_content_type')),
    sa.PrimaryKeyConstraint('id', name=op.f('search_keywords_stats_pkey')),
    sa.UniqueConstraint('keyword', 'content_type', 'stat_date', name=op.f('uq_keyword_type_date'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_search_keywords_stats_id'), 'search_keywords_stats', ['id'], unique=False)
    op.create_index(op.f('idx_search_keywords_stats_keyword'), 'search_keywords_stats', ['keyword', 'content_type'], unique=False)
    op.create_index(op.f('idx_search_keywords_stats_date'), 'search_keywords_stats', ['stat_date'], unique=False)
    op.create_index(op.f('idx_search_keywords_stats_count'), 'search_keywords_stats', ['search_count', 'stat_date'], unique=False)
    op.create_table('admin_analytics_snapshots',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('admin_analytics_snapshots_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('snapshot_type', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
    sa.Column('time_range', sa.VARCHAR(length=32), server_default=sa.text("'last_7d'::character varying"), autoincrement=False, nullable=False),
    sa.Column('filters', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('record_count', sa.BIGINT(), server_default=sa.text("'0'::bigint"), autoincrement=False, nullable=False),
    sa.Column('generated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('generation_duration', sa.BIGINT(), server_default=sa.text("'0'::bigint"), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=32), server_default=sa.text("'pending'::character varying"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='admin_analytics_snapshots_pkey'),
    sa.UniqueConstraint('snapshot_type', 'time_range', name='uq_admin_analytics_snapshots_type_range', postgresql_include=[], postgresql_nulls_not_distinct=False),
    comment='后台运营分析快照概览',
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_admin_analytics_snapshots_type'), 'admin_analytics_snapshots', ['snapshot_type'], unique=False)
    op.create_index(op.f('ix_admin_analytics_snapshots_time_range'), 'admin_analytics_snapshots', ['time_range'], unique=False)
    op.create_index(op.f('ix_admin_analytics_snapshots_status'), 'admin_analytics_snapshots', ['status'], unique=False)
    op.create_index(op.f('ix_admin_analytics_snapshots_generated_at'), 'admin_analytics_snapshots', ['generated_at'], unique=False)
    op.create_table('admin_monitoring_metrics',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('metric_name', sa.VARCHAR(length=150), autoincrement=False, nullable=False),
    sa.Column('value', sa.NUMERIC(precision=18, scale=6), autoincrement=False, nullable=False),
    sa.Column('labels', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('source', sa.VARCHAR(length=64), server_default=sa.text("'system'::character varying"), autoincrement=False, nullable=False),
    sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('admin_monitoring_metrics_pkey')),
    comment='监控指标时序数据'
    )
    op.create_index(op.f('ix_admin_monitoring_metrics_timestamp'), 'admin_monitoring_metrics', ['timestamp'], unique=False)
    op.create_index(op.f('ix_admin_monitoring_metrics_source'), 'admin_monitoring_metrics', ['source'], unique=False)
    op.create_index(op.f('ix_admin_monitoring_metrics_metric_timestamp'), 'admin_monitoring_metrics', ['metric_name', 'timestamp'], unique=False)
    op.create_index(op.f('ix_admin_monitoring_metrics_metric_name'), 'admin_monitoring_metrics', ['metric_name'], unique=False)
    op.create_table('audit_logs',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('user_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('user_email', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('ip_address', sa.VARCHAR(length=45), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('request_id', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('session_id', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('action', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('resource_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('resource_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('resource_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('reason', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('old_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('new_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('success', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('endpoint', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('method', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('duration_ms', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('audit_logs_pkey')),
    comment='审计日志表'
    )
    op.create_index(op.f('ix_audit_logs_user_timestamp'), 'audit_logs', ['user_id', 'timestamp'], unique=False)
    op.create_index(op.f('ix_audit_logs_user_id'), 'audit_logs', ['user_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_timestamp'), 'audit_logs', ['timestamp'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource_type'), 'audit_logs', ['resource_type'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource_timestamp'), 'audit_logs', ['resource_type', 'resource_id', 'timestamp'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource_id'), 'audit_logs', ['resource_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_request_id'), 'audit_logs', ['request_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_id'), 'audit_logs', ['id'], unique=False)
    op.create_index(op.f('ix_audit_logs_action_timestamp'), 'audit_logs', ['action', 'timestamp'], unique=False)
    op.create_index(op.f('ix_audit_logs_action'), 'audit_logs', ['action'], unique=False)
    op.create_table('topic_stats',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('topic', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('post_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('total_likes', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('total_comments', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('total_reposts', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('total_views', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('hot_score', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('trend_score', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('last_post_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('peak_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text("timezone('utc'::text, now())"), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text("timezone('utc'::text, now())"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('topic_stats_pkey')),
    sa.UniqueConstraint('topic', name=op.f('topic_stats_topic_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('idx_topic_stats_updated_at'), 'topic_stats', ['updated_at'], unique=False)
    op.create_index(op.f('idx_topic_stats_trend_score'), 'topic_stats', ['trend_score'], unique=False)
    op.create_index(op.f('idx_topic_stats_post_count'), 'topic_stats', ['post_count'], unique=False)
    op.create_index(op.f('idx_topic_stats_last_post_at'), 'topic_stats', ['last_post_at'], unique=False)
    op.create_index(op.f('idx_topic_stats_hot_score'), 'topic_stats', ['hot_score'], unique=False)
    op.create_table('admin_alert_rules',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('admin_alert_rules_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=150), autoincrement=False, nullable=False),
    sa.Column('metric_name', sa.VARCHAR(length=150), autoincrement=False, nullable=False),
    sa.Column('operator', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('threshold', sa.NUMERIC(precision=18, scale=6), autoincrement=False, nullable=False),
    sa.Column('duration', sa.VARCHAR(length=32), server_default=sa.text("'0m'::character varying"), autoincrement=False, nullable=False),
    sa.Column('severity', sa.VARCHAR(length=32), server_default=sa.text("'info'::character varying"), autoincrement=False, nullable=False),
    sa.Column('channels', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'[]'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('enabled', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name='admin_alert_rules_created_by_fkey', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], name='admin_alert_rules_updated_by_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='admin_alert_rules_pkey'),
    sa.UniqueConstraint('name', name='uq_admin_alert_rules_name', postgresql_include=[], postgresql_nulls_not_distinct=False),
    comment='系统监控告警规则定义',
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_admin_alert_rules_severity'), 'admin_alert_rules', ['severity'], unique=False)
    op.create_index(op.f('ix_admin_alert_rules_metric_name'), 'admin_alert_rules', ['metric_name'], unique=False)
    op.create_index(op.f('ix_admin_alert_rules_enabled'), 'admin_alert_rules', ['enabled'], unique=False)
    op.create_index(op.f('ix_admin_alert_rules_created_at'), 'admin_alert_rules', ['created_at'], unique=False)
    op.create_table('search_suggestions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('keyword', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='搜索关键词'),
    sa.Column('content_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False, comment='内容类型'),
    sa.Column('suggestion_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False, comment='建议类型'),
    sa.Column('weight', sa.INTEGER(), autoincrement=False, nullable=True, comment='权重值，用于排序'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否激活'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.CheckConstraint("content_type::text = ANY (ARRAY['article'::character varying, 'video'::character varying, 'scratch'::character varying, 'all'::character varying]::text[])", name=op.f('check_suggestions_content_type')),
    sa.CheckConstraint("suggestion_type::text = ANY (ARRAY['trending'::character varying, 'popular'::character varying, 'related'::character varying]::text[])", name=op.f('check_suggestion_type')),
    sa.PrimaryKeyConstraint('id', name=op.f('search_suggestions_pkey')),
    sa.UniqueConstraint('keyword', 'content_type', 'suggestion_type', name=op.f('uq_keyword_type_suggestion'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_search_suggestions_id'), 'search_suggestions', ['id'], unique=False)
    op.create_index(op.f('idx_search_suggestions_weight'), 'search_suggestions', ['weight', 'content_type'], unique=False)
    op.create_index(op.f('idx_search_suggestions_keyword'), 'search_suggestions', ['keyword', 'content_type', 'is_active'], unique=False)
    op.create_table('post_poll_votes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('post_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('option_index', sa.INTEGER(), autoincrement=False, nullable=False, comment='投票选项索引'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['post_id'], ['posts.id'], name=op.f('post_poll_votes_post_id_fkey')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('post_poll_votes_user_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('post_poll_votes_pkey'))
    )
    op.create_index(op.f('ix_post_poll_votes_id'), 'post_poll_votes', ['id'], unique=False)
    op.create_index(op.f('idx_post_poll_votes_user_id'), 'post_poll_votes', ['user_id'], unique=False)
    op.create_index(op.f('idx_post_poll_votes_unique'), 'post_poll_votes', ['post_id', 'user_id', 'option_index'], unique=True)
    op.create_index(op.f('idx_post_poll_votes_post_id'), 'post_poll_votes', ['post_id'], unique=False)
    op.create_table('admin_alert_events',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('rule_id', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('event_id', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=32), server_default=sa.text("'active'::character varying"), autoincrement=False, nullable=False),
    sa.Column('trigger_value', sa.NUMERIC(precision=18, scale=6), autoincrement=False, nullable=False),
    sa.Column('triggered_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('resolved_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('context', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('notification_status', sa.VARCHAR(length=32), server_default=sa.text("'pending'::character varying"), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['rule_id'], ['admin_alert_rules.id'], name=op.f('admin_alert_events_rule_id_fkey'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('admin_alert_events_pkey')),
    sa.UniqueConstraint('event_id', name=op.f('uq_admin_alert_events_event_id'), postgresql_include=[], postgresql_nulls_not_distinct=False),
    comment='监控告警触发事件'
    )
    op.create_index(op.f('ix_admin_alert_events_triggered_at'), 'admin_alert_events', ['triggered_at'], unique=False)
    op.create_index(op.f('ix_admin_alert_events_status'), 'admin_alert_events', ['status'], unique=False)
    op.create_index(op.f('ix_admin_alert_events_rule_id'), 'admin_alert_events', ['rule_id'], unique=False)
    op.create_index(op.f('ix_admin_alert_events_notification_status'), 'admin_alert_events', ['notification_status'], unique=False)
    op.create_table('user_interests',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('interest_tag', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('interest_weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('user_interests_user_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('user_interests_pkey'))
    )
    op.create_index(op.f('ix_user_interests_user_id'), 'user_interests', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_interests_interest_tag'), 'user_interests', ['interest_tag'], unique=False)
    op.create_index(op.f('ix_user_interests_id'), 'user_interests', ['id'], unique=False)
    op.create_table('backpack_items',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False),
    sa.Column('type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('mime', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('body_path', sa.VARCHAR(length=512), autoincrement=False, nullable=False),
    sa.Column('thumbnail_path', sa.VARCHAR(length=512), autoincrement=False, nullable=False),
    sa.Column('size_bytes', sa.BIGINT(), server_default=sa.text('0'), autoincrement=False, nullable=False),
    sa.Column('extra_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='所属用户ID'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_backpack_items_user_id'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('backpack_items_pkey'))
    )
    op.create_index(op.f('ix_backpack_items_user_id_type'), 'backpack_items', ['user_id', 'type'], unique=False)
    op.create_index(op.f('ix_backpack_items_user_id'), 'backpack_items', ['user_id'], unique=False)
    op.create_table('admin_analytics_snapshot_data',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('snapshot_id', sa.BIGINT(), autoincrement=False, nullable=False),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.VARCHAR(length=64), server_default=sa.text("'summary'::character varying"), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['snapshot_id'], ['admin_analytics_snapshots.id'], name=op.f('admin_analytics_snapshot_data_snapshot_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('admin_analytics_snapshot_data_pkey')),
    comment='后台运营分析快照具体数据载荷'
    )
    op.create_index(op.f('ix_admin_analytics_snapshot_data_snapshot_id'), 'admin_analytics_snapshot_data', ['snapshot_id'], unique=False)
    op.create_index(op.f('ix_admin_analytics_snapshot_data_data_type'), 'admin_analytics_snapshot_data', ['data_type'], unique=False)
    op.create_index(op.f('ix_admin_analytics_snapshot_data_created_at'), 'admin_analytics_snapshot_data', ['created_at'], unique=False)
    op.create_table('notifications',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('type', postgresql.ENUM('info', 'success', 'warning', 'error', 'task_complete', 'new_message', 'system_update', 'content_recommendation', 'account_activity', name='notificationtype'), autoincrement=False, nullable=False),
    sa.Column('priority', postgresql.ENUM('low', 'normal', 'high', 'urgent', name='notificationpriority'), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(length=200), autoincrement=False, nullable=False),
    sa.Column('message', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('unread', 'read', 'deleted', name='notificationstatus'), autoincrement=False, nullable=True),
    sa.Column('data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('action_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('read_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('notifications_user_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('notifications_pkey'))
    )
    op.create_index(op.f('ix_notifications_user_status'), 'notifications', ['user_id', 'status'], unique=False)
    op.create_index(op.f('ix_notifications_user_id'), 'notifications', ['user_id'], unique=False)
    op.create_index(op.f('ix_notifications_type'), 'notifications', ['type'], unique=False)
    op.create_index(op.f('ix_notifications_status'), 'notifications', ['status'], unique=False)
    op.create_index(op.f('ix_notifications_priority'), 'notifications', ['priority'], unique=False)
    op.create_index(op.f('ix_notifications_created_at'), 'notifications', ['created_at'], unique=False)
    op.create_table('admin_config_settings',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('key', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('value', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('value_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('version', sa.BIGINT(), server_default=sa.text("'1'::bigint"), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_sensitive', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
    sa.CheckConstraint('version >= 1', name=op.f('ck_admin_config_settings_version_positive')),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], name=op.f('admin_config_settings_updated_by_fkey'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('admin_config_settings_pkey')),
    sa.UniqueConstraint('key', name=op.f('uq_admin_config_settings_key'), postgresql_include=[], postgresql_nulls_not_distinct=False),
    comment='系统配置项存储表'
    )
    op.create_index(op.f('ix_admin_config_settings_is_sensitive'), 'admin_config_settings', ['is_sensitive'], unique=False)
    op.create_index(op.f('ix_admin_config_settings_category_key'), 'admin_config_settings', ['category', 'key'], unique=False)
    op.create_index(op.f('ix_admin_config_settings_category'), 'admin_config_settings', ['category'], unique=False)
    op.create_table('post_mentions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('post_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('mentioned_user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('mention_text', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='@提及的文本'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['mentioned_user_id'], ['users.id'], name=op.f('post_mentions_mentioned_user_id_fkey')),
    sa.ForeignKeyConstraint(['post_id'], ['posts.id'], name=op.f('post_mentions_post_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('post_mentions_pkey'))
    )
    op.create_index(op.f('ix_post_mentions_id'), 'post_mentions', ['id'], unique=False)
    op.create_index(op.f('idx_post_mentions_user_id'), 'post_mentions', ['mentioned_user_id'], unique=False)
    op.create_index(op.f('idx_post_mentions_post_id'), 'post_mentions', ['post_id'], unique=False)
    op.create_table('scratch_products',
    sa.Column('project_id', sa.INTEGER(), server_default=sa.text("nextval('scratch_products_project_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('cover_url', sa.VARCHAR(length=512), autoincrement=False, nullable=True, comment='封面图URL'),
    sa.Column('author_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('is_published', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('cache_version', sa.INTEGER(), server_default=sa.text('1'), autoincrement=False, nullable=False, comment='缓存版本号'),
    sa.Column('visit_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='访问次数'),
    sa.Column('adapt_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('favorite_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('can_adapt', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('original_project_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('root_project_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('adapt_level', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False, comment='改编层级，0为原创'),
    sa.Column('adaptation_type', sa.VARCHAR(length=20), server_default=sa.text("'''original'''::character varying"), autoincrement=False, nullable=False, comment='改编类型'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('like_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False, comment='点赞次数'),
    sa.Column('code', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='项目代码'),
    sa.Column('category', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    sa.Column('difficulty', sa.VARCHAR(length=32), autoincrement=False, nullable=True),
    sa.Column('badges', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('highlights', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='项目亮点列表'),
    sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True, comment='全文搜索向量'),
    sa.CheckConstraint('adapt_level >= 0 AND adapt_level <= 5', name='check_adapt_level_limit'),
    sa.CheckConstraint('project_id <> original_project_id', name='check_no_self_reference'),
    sa.ForeignKeyConstraint(['author_id'], ['users.id'], name='fk_scratch_products_author_id'),
    sa.ForeignKeyConstraint(['original_project_id'], ['scratch_products.project_id'], name='fk_scratch_products_original_project_id'),
    sa.ForeignKeyConstraint(['root_project_id'], ['scratch_products.project_id'], name='fk_scratch_products_root_project_id'),
    sa.PrimaryKeyConstraint('project_id', name='scratch_products_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_scratch_title'), 'scratch_products', ['title'], unique=False)
    op.create_index(op.f('ix_scratch_is_published'), 'scratch_products', ['is_published'], unique=False)
    op.create_index(op.f('ix_scratch_author_id'), 'scratch_products', ['author_id'], unique=False)
    op.create_index(op.f('idx_scratch_root_project'), 'scratch_products', ['root_project_id'], unique=False)
    op.create_index(op.f('idx_scratch_products_title_trgm'), 'scratch_products', ['title'], unique=False, postgresql_using='gist')
    op.create_index(op.f('idx_scratch_products_search_vector'), 'scratch_products', ['search_vector'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_scratch_original_project'), 'scratch_products', ['original_project_id'], unique=False)
    op.create_index(op.f('idx_scratch_adaptation_type'), 'scratch_products', ['adaptation_type'], unique=False)
    op.create_index(op.f('idx_scratch_adapt_level'), 'scratch_products', ['adapt_level'], unique=False)
    op.create_table('tag_stats',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('tag_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('content_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False, comment='标签所属内容类型: article/video/scratch/post/global'),
    sa.Column('total_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False, comment='累计热度分数'),
    sa.Column('unique_users_est', sa.INTEGER(), autoincrement=False, nullable=False, comment='独立用户估算值'),
    sa.Column('window_delta', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False, comment='最近窗口热度变化'),
    sa.Column('last_seen_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='最后一次热度更新的时间'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], name=op.f('tag_stats_tag_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('tag_stats_pkey')),
    sa.UniqueConstraint('tag_id', 'content_type', name=op.f('uq_tag_stats_tag_content_type'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_tag_stats_tag_id'), 'tag_stats', ['tag_id'], unique=False)
    op.create_index(op.f('ix_tag_stats_id'), 'tag_stats', ['id'], unique=False)
    op.create_index(op.f('ix_tag_stats_content_type'), 'tag_stats', ['content_type'], unique=False)
    op.create_table('admin_config_change_logs',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('change_id', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('config_key', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('old_value', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('new_value', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('change_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('change_reason', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('changed_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('changed_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('rollback_id', sa.VARCHAR(length=128), autoincrement=False, nullable=True),
    sa.Column('is_rollback', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['changed_by'], ['users.id'], name=op.f('admin_config_change_logs_changed_by_fkey'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['rollback_id'], ['admin_config_change_logs.change_id'], name=op.f('admin_config_change_logs_rollback_id_fkey'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('admin_config_change_logs_pkey')),
    sa.UniqueConstraint('change_id', name=op.f('uq_admin_config_change_logs_change_id'), postgresql_include=[], postgresql_nulls_not_distinct=False),
    comment='配置变更历史与回滚记录'
    )
    op.create_index(op.f('ix_admin_config_change_logs_rollback_id'), 'admin_config_change_logs', ['rollback_id'], unique=False)
    op.create_index(op.f('ix_admin_config_change_logs_config_key'), 'admin_config_change_logs', ['config_key'], unique=False)
    op.create_index(op.f('ix_admin_config_change_logs_changed_by'), 'admin_config_change_logs', ['changed_by'], unique=False)
    op.create_index(op.f('ix_admin_config_change_logs_changed_at'), 'admin_config_change_logs', ['changed_at'], unique=False)
    op.create_index(op.f('ix_admin_config_change_logs_category_changed_at'), 'admin_config_change_logs', ['category', 'changed_at'], unique=False)
    op.create_table('post_media',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('post_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('media_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='媒体类型: image, video, audio'),
    sa.Column('media_url', sa.VARCHAR(length=500), autoincrement=False, nullable=False, comment='媒体文件URL'),
    sa.Column('thumbnail_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='缩略图URL'),
    sa.Column('media_size', sa.INTEGER(), autoincrement=False, nullable=True, comment='文件大小（字节）'),
    sa.Column('media_width', sa.INTEGER(), autoincrement=False, nullable=True, comment='媒体宽度'),
    sa.Column('media_height', sa.INTEGER(), autoincrement=False, nullable=True, comment='媒体高度'),
    sa.Column('media_duration', sa.INTEGER(), autoincrement=False, nullable=True, comment='媒体时长（秒）'),
    sa.Column('sort_order', sa.INTEGER(), autoincrement=False, nullable=True, comment='排序顺序'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['post_id'], ['posts.id'], name=op.f('post_media_post_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('post_media_pkey'))
    )
    op.create_index(op.f('ix_post_media_id'), 'post_media', ['id'], unique=False)
    op.create_index(op.f('idx_post_media_type'), 'post_media', ['media_type'], unique=False)
    op.create_index(op.f('idx_post_media_post_id'), 'post_media', ['post_id'], unique=False)
    op.create_table('outbox_messages',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('topic', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='消息主题，如 user.updated'),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False, comment='消息内容'),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='消息状态 (pending, sent, failed)'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('outbox_messages_pkey'))
    )
    op.create_index(op.f('ix_outbox_messages_topic'), 'outbox_messages', ['topic'], unique=False)
    op.create_index(op.f('ix_outbox_messages_status'), 'outbox_messages', ['status'], unique=False)
    op.create_index(op.f('ix_outbox_messages_id'), 'outbox_messages', ['id'], unique=False)
    op.create_table('scratch_project_tags',
    sa.Column('project_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('tag_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['scratch_products.project_id'], name=op.f('scratch_project_tags_project_id_fkey')),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], name=op.f('scratch_project_tags_tag_id_fkey')),
    sa.PrimaryKeyConstraint('project_id', 'tag_id', name=op.f('scratch_project_tags_pkey'))
    )
    op.create_table('admin_feature_toggles',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('is_enabled', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('scope', sa.VARCHAR(length=50), server_default=sa.text("'global'::character varying"), autoincrement=False, nullable=False),
    sa.Column('config', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb"), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], name=op.f('admin_feature_toggles_updated_by_fkey'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('admin_feature_toggles_pkey')),
    sa.UniqueConstraint('name', name=op.f('uq_admin_feature_toggles_name'), postgresql_include=[], postgresql_nulls_not_distinct=False),
    comment='后台功能开关管理表'
    )
    op.create_index(op.f('ix_admin_feature_toggles_scope'), 'admin_feature_toggles', ['scope'], unique=False)
    op.create_index(op.f('ix_admin_feature_toggles_is_enabled'), 'admin_feature_toggles', ['is_enabled'], unique=False)
    op.create_table('search_history',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('query', sa.TEXT(), autoincrement=False, nullable=False, comment='搜索关键词'),
    sa.Column('content_type', sa.VARCHAR(length=32), autoincrement=False, nullable=False, comment='搜索内容类型'),
    sa.Column('filters', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='搜索过滤条件（JSON格式）'),
    sa.Column('result_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='搜索结果数量'),
    sa.Column('clicked_result_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='用户点击的结果ID'),
    sa.Column('clicked_result_position', sa.INTEGER(), autoincrement=False, nullable=True, comment='点击结果的位置'),
    sa.Column('session_id', sa.VARCHAR(length=128), autoincrement=False, nullable=True, comment='会话ID'),
    sa.Column('ip_address', postgresql.INET(), autoincrement=False, nullable=True, comment='IP地址'),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True, comment='用户代理'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.CheckConstraint("content_type::text = ANY (ARRAY['article'::character varying, 'video'::character varying, 'scratch'::character varying, 'all'::character varying]::text[])", name=op.f('check_content_type')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('search_history_user_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('search_history_pkey'))
    )
    op.create_index(op.f('ix_search_history_user_id'), 'search_history', ['user_id'], unique=False)
    op.create_index(op.f('ix_search_history_id'), 'search_history', ['id'], unique=False)
    op.create_index(op.f('idx_search_history_user'), 'search_history', ['user_id', 'created_at'], unique=False)
    op.create_index(op.f('idx_search_history_query'), 'search_history', ['query', 'content_type'], unique=False)
    op.create_index(op.f('idx_search_history_created'), 'search_history', ['created_at'], unique=False)
    op.create_table('posts',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('author_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=True, comment='沸点文本内容'),
    sa.Column('post_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='沸点类型'),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='沸点状态'),
    sa.Column('visibility', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='可见性'),
    sa.Column('original_post_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='原始沸点ID（转发时使用）'),
    sa.Column('repost_comment', sa.TEXT(), autoincrement=False, nullable=True, comment='转发评论'),
    sa.Column('topic', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='话题标签'),
    sa.Column('location', sa.VARCHAR(length=200), autoincrement=False, nullable=True, comment='位置信息'),
    sa.Column('link_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='分享链接URL'),
    sa.Column('link_title', sa.VARCHAR(length=200), autoincrement=False, nullable=True, comment='链接标题'),
    sa.Column('link_description', sa.TEXT(), autoincrement=False, nullable=True, comment='链接描述'),
    sa.Column('link_image', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='链接预览图'),
    sa.Column('poll_options', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='投票选项JSON'),
    sa.Column('poll_expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='投票截止时间'),
    sa.Column('poll_multiple_choice', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否允许多选'),
    sa.Column('like_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='点赞数'),
    sa.Column('comment_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='评论数'),
    sa.Column('repost_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='转发数'),
    sa.Column('view_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='浏览数'),
    sa.Column('is_pinned', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否置顶'),
    sa.Column('is_hot', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否热门'),
    sa.Column('hot_score', sa.INTEGER(), autoincrement=False, nullable=True, comment='热度分数'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('cache_version', sa.INTEGER(), server_default=sa.text('1'), autoincrement=False, nullable=False, comment='缓存版本号'),
    sa.ForeignKeyConstraint(['author_id'], ['users.id'], name=op.f('posts_author_id_fkey')),
    sa.ForeignKeyConstraint(['original_post_id'], ['posts.id'], name=op.f('posts_original_post_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('posts_pkey'))
    )
    op.create_index(op.f('ix_posts_id'), 'posts', ['id'], unique=False)
    op.create_index(op.f('idx_posts_visibility_status'), 'posts', ['visibility', 'status'], unique=False)
    op.create_index(op.f('idx_posts_type_created'), 'posts', ['post_type', 'created_at'], unique=False)
    op.create_index(op.f('idx_posts_topic'), 'posts', ['topic'], unique=False)
    op.create_index(op.f('idx_posts_status_created'), 'posts', ['status', 'created_at'], unique=False)
    op.create_index(op.f('idx_posts_hot_score'), 'posts', ['hot_score'], unique=False)
    op.create_index(op.f('idx_posts_author_created'), 'posts', ['author_id', 'created_at'], unique=False)
    # ### end Alembic commands ###
