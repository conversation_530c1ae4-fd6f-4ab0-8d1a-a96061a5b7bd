# 后端 API 需求文档：评论与帖子提及功能

## 概述

本文档描述了评论和帖子中 @ 提及用户功能所需的后端 API 支持。前端已完成提及功能的实现，需要后端提供相应的 API 接口来支持用户搜索、提及验证和通知发送。

## 目录

1. [提及候选查询 API](#1-提及候选查询-api)
2. [评论创建 API 更新](#2-评论创建-api-更新)
3. [帖子创建 API 更新](#3-帖子创建-api-更新)
4. [提及通知 API](#4-提及通知-api)
5. [错误码规范](#5-错误码规范)
6. [数据模型](#6-数据模型)

---

## 1. 提及候选查询 API

### 1.1 获取提及候选用户列表

**端点**: `GET /api/v1/users/mentions`

**描述**: 根据关键字搜索可提及的用户列表，支持分页和游标查询。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| q | string | 是 | 搜索关键字（用户名或昵称） |
| limit | integer | 否 | 返回结果数量，默认 10，最大 50 |
| cursor | string | 否 | 分页游标，用于加载更多结果 |

**请求示例**:
```http
GET /api/v1/users/mentions?q=张三&limit=10
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "123",
        "display_name": "张三",
        "avatar_url": "https://example.com/avatar.jpg",
        "role_label": "管理员",
        "is_active": true
      }
    ],
    "next_cursor": "eyJpZCI6MTIzfQ==",
    "total_count": 25
  }
}
```

**响应字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| items | array | 用户候选列表 |
| items[].id | string | 用户 ID |
| items[].display_name | string | 显示名称（昵称或用户名） |
| items[].avatar_url | string\|null | 用户头像 URL |
| items[].role_label | string\|null | 角色标签（如"管理员"、"VIP"等） |
| items[].is_active | boolean | 用户是否激活 |
| next_cursor | string\|null | 下一页游标 |
| total_count | integer | 总结果数 |

**业务规则**:
- 搜索应匹配用户名和昵称
- 只返回激活状态的用户
- 支持模糊搜索
- 结果按相关度排序
- 应过滤掉被当前用户屏蔽的用户
- 应过滤掉屏蔽了当前用户的用户

**性能要求**:
- 响应时间 < 200ms
- 支持防抖查询（前端已实现 160ms 防抖）

---

## 2. 评论创建 API 更新

### 2.1 创建评论（文章/视频/Scratch）

**端点**: `POST /api/v1/comments/`

**描述**: 创建评论时支持提及用户。

**请求体更新**:
```json
{
  "content": "这是一条评论 [@张三](user://123)",
  "comment_type": "article",
  "article_id": 456,
  "image_urls": [],
  "parent_id": null,
  "reply_to_id": null,
  "mentions": ["123", "456"]
}
```

**新增字段**:

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| mentions | array[string] | 否 | 被提及的用户 ID 列表 |

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 789,
    "content": "这是一条评论 [@张三](user://123)",
    "mentions": [
      {
        "id": "123",
        "username": "zhangsan",
        "nickname": "张三"
      }
    ],
    ...
  }
}
```

### 2.2 创建回复

**端点**: `POST /api/v1/comments/reply`

**描述**: 创建回复时支持提及用户。

**请求体更新**: 同 2.1，增加 `mentions` 字段。

---

## 3. 帖子创建 API 更新

### 3.1 创建沸点

**端点**: `POST /api/v1/posts`

**描述**: 创建沸点时支持提及用户。

**请求体更新**:
```json
{
  "content": "分享一个想法 [@李四](user://456)",
  "post_type": "text",
  "visibility": "public",
  "topic": "技术分享",
  "location": "北京",
  "mentions": ["456"]
}
```

**新增字段**:

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| mentions | array[string] | 否 | 被提及的用户 ID 列表 |

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 1001,
    "content": "分享一个想法 [@李四](user://456)",
    "mentions": [
      {
        "id": "456",
        "username": "lisi",
        "nickname": "李四"
      }
    ],
    ...
  }
}
```

### 3.2 创建沸点评论

**端点**: `POST /api/v1/posts/{post_id}/comments`

**描述**: 在沸点下创建评论时支持提及用户。

**请求体更新**:
```json
{
  "content": "同意 [@王五](user://789)",
  "parent_id": null,
  "image_urls": [],
  "mentions": ["789"]
}
```

**新增字段**: 同上，增加 `mentions` 字段。

---

## 4. 提及通知 API

### 4.1 发送提及通知

**业务逻辑**: 当用户在评论或帖子中被提及时，系统应自动创建通知。

**通知类型**: `mention`

**通知数据结构**:
```json
{
  "id": "notif_123",
  "type": "mention",
  "sender": {
    "id": "123",
    "name": "张三",
    "avatar": "https://example.com/avatar.jpg"
  },
  "title": "张三 在评论中提到了你",
  "content": "这是一条评论 [@你的昵称](user://your_id)",
  "excerpt": "原文内容摘要...",
  "target": {
    "type": "comment",
    "id": 789,
    "href": "/articles/456#comment-789",
    "label": "查看评论",
    "title": "文章标题"
  },
  "status": "unread",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### 4.2 获取提及通知

**端点**: `GET /api/v1/notifications`

**描述**: 获取用户的通知列表，包括提及通知。

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 过滤通知类型，如 `mention` |
| status | string | 否 | 过滤状态：`unread`、`read`、`all` |
| page | integer | 否 | 页码，默认 1 |
| size | integer | 否 | 每页数量，默认 20 |

---

## 5. 错误码规范

### 5.1 提及相关错误码

| 错误码 | HTTP 状态码 | 说明 | 前端处理 |
|--------|-------------|------|----------|
| `MENTION_USER_NOT_FOUND` | 404 | 提及的用户不存在 | 提示"提及的用户不存在" |
| `MENTION_USER_INACTIVE` | 400 | 提及的用户已停用 | 提示"提及的用户已停用" |
| `MENTION_PERMISSION_DENIED` | 403 | 无权限提及该用户 | 提示"无权限提及该用户" |
| `MENTION_INVALID_FORMAT` | 400 | 提及格式无效 | 提示"提及格式错误" |
| `MENTION_TOO_MANY_MENTIONS` | 400 | 提及用户数量超限 | 提示"提及用户过多" |

### 5.2 错误响应格式

```json
{
  "success": false,
  "code": "MENTION_USER_NOT_FOUND",
  "message": "提及的用户不存在",
  "details": {
    "user_id": "123",
    "field": "mentions"
  }
}
```

---

## 6. 数据模型

### 6.1 提及内容格式

前端使用 Markdown 链接格式存储提及：

```
[@用户昵称](user://用户ID)
```

**示例**:
```
你好 [@张三](user://123)，这是一条测试消息。
```

### 6.2 数据库设计建议

#### 6.2.1 评论表（comments）

```sql
ALTER TABLE comments ADD COLUMN mentions JSONB;
```

**mentions 字段示例**:
```json
["123", "456", "789"]
```

#### 6.2.2 帖子表（posts）

```sql
ALTER TABLE posts ADD COLUMN mentions JSONB;
```

#### 6.2.3 提及记录表（mentions）- 可选

如果需要更复杂的查询和统计，可以创建独立的提及记录表：

```sql
CREATE TABLE mentions (
  id BIGSERIAL PRIMARY KEY,
  content_type VARCHAR(20) NOT NULL, -- 'comment', 'post'
  content_id BIGINT NOT NULL,
  mentioned_user_id BIGINT NOT NULL,
  mentioner_user_id BIGINT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_mentioned_user (mentioned_user_id),
  INDEX idx_content (content_type, content_id)
);
```

### 6.3 业务规则

#### 6.3.1 提及验证

- 验证所有提及的用户 ID 是否存在
- 验证用户是否处于激活状态
- 验证当前用户是否有权限提及目标用户
- 单条内容最多提及 10 个用户

#### 6.3.2 通知规则

- 用户被提及时立即发送通知
- 不向自己发送提及通知
- 如果用户屏蔽了提及者，不发送通知
- 支持通知偏好设置（用户可关闭提及通知）

#### 6.3.3 权限控制

- 被封禁用户不能提及他人
- 私密内容中的提及不发送通知
- 草稿中的提及不发送通知

---

## 7. 实现优先级

### P0（必须实现）
1. ✅ 提及候选查询 API (`GET /users/mentions`)
2. ✅ 评论创建 API 支持 mentions 字段
3. ✅ 帖子创建 API 支持 mentions 字段
4. ✅ 基础错误码处理

### P1（重要）
5. 提及通知发送
6. 提及用户验证逻辑
7. 提及记录存储

### P2（优化）
8. 提及统计和分析
9. 提及权限细粒度控制
10. 提及通知偏好设置

---

## 8. 测试用例

### 8.1 提及候选查询

**测试场景**:
- 搜索存在的用户
- 搜索不存在的用户
- 空关键字查询
- 分页查询
- 特殊字符处理

### 8.2 提及创建

**测试场景**:
- 提及单个用户
- 提及多个用户
- 提及不存在的用户（应返回错误）
- 提及已停用的用户（应返回错误）
- 提及超过限制数量的用户（应返回错误）
- 自己提及自己（应允许但不发通知）

### 8.3 提及通知

**测试场景**:
- 被提及后收到通知
- 自己提及自己不收到通知
- 被屏蔽用户提及不收到通知
- 私密内容提及不收到通知

---

## 9. 性能考虑

### 9.1 查询优化

- 用户搜索应使用全文索引
- 提及验证应批量查询
- 通知发送应异步处理

### 9.2 缓存策略

- 热门用户列表可缓存 5 分钟
- 用户基本信息可缓存 10 分钟

### 9.3 限流

- 提及候选查询：每用户 60 次/分钟
- 评论/帖子创建：每用户 10 次/分钟

---

## 10. 安全考虑

### 10.1 输入验证

- 验证 mentions 数组长度
- 验证用户 ID 格式
- 防止 SQL 注入
- 防止 XSS 攻击（内容已在前端序列化）

### 10.2 权限验证

- 验证当前用户身份
- 验证提及权限
- 验证内容访问权限

---

## 11. 前端已实现功能

前端已完成以下功能，后端需要配合：

✅ 提及输入和候选列表展示  
✅ 提及内容序列化（Markdown 格式）  
✅ 提及内容解析和渲染  
✅ 评论和帖子提交时包含 mentions 字段  
✅ 错误处理和用户提示  
✅ 消息中心提及内容展示  
✅ 移动端适配  
✅ 可访问性支持（ARIA 属性）

---

## 12. 联系方式

如有疑问，请联系前端团队：
- 文档版本：v1.0
- 更新日期：2024-01-15
- 前端实现分支：`feature/comment-post-mentions`

---

## 附录 A：API 调用流程图

```
用户输入 @ 
    ↓
前端触发搜索
    ↓
GET /users/mentions?q=关键字
    ↓
后端返回候选列表
    ↓
用户选择候选
    ↓
前端序列化内容
    ↓
POST /comments/ 或 /posts/
    ↓
后端验证 mentions
    ↓
后端保存内容
    ↓
后端发送提及通知
    ↓
被提及用户收到通知
```

## 附录 B：数据流示例

### 创建带提及的评论

**前端发送**:
```json
{
  "content": "同意 [@张三](user://123) 的观点",
  "comment_type": "article",
  "article_id": 456,
  "mentions": ["123"]
}
```

**后端处理**:
1. 验证用户 123 存在且激活
2. 保存评论内容（包含 Markdown 格式）
3. 保存 mentions 字段
4. 创建提及通知发送给用户 123

**后端返回**:
```json
{
  "success": true,
  "data": {
    "id": 789,
    "content": "同意 [@张三](user://123) 的观点",
    "mentions": [
      {
        "id": "123",
        "username": "zhangsan",
        "nickname": "张三"
      }
    ],
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```
