"""
Tests for mention functionality in comments and user search API
"""

import pytest
from fastapi import FastAP<PERSON>, status
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, MagicMock, patch

# Imports for mention functionality testing
from app.api.endpoints.users import router as users_router


@pytest.fixture
def test_app():
    """Test application"""
    app = FastAPI()
    app.include_router(users_router, prefix="/api/v1/users", tags=["users"])
    return app


@pytest.fixture
def client(test_app):
    """Test client"""
    return TestClient(test_app)


def test_search_mention_candidates_success(client: TestClient):
    """Test successful user mention search"""
    # Mock users data
    mock_users = [
        MagicMock(
            id=1,
            username="testuser1",
            nickname="Test User 1",
            avatar="avatar1.jpg",
            role=MagicMock(name="user", desc=None),
            is_active=True
        ),
        MagicMock(
            id=2,
            username="testuser2",
            nickname="Test User 2",
            avatar="avatar2.jpg",
            role=MagicMock(name="admin", desc="管理员"),
            is_active=True
        ),
    ]
    
    with patch("app.api.endpoints.users.select") as mock_select, \
         patch("app.api.endpoints.users.func") as mock_func:
        
        # Mock database query
        mock_query = MagicMock()
        mock_select.return_value = mock_query
        mock_query.where.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        
        # Mock database execution
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_users
        
        # Mock count query
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 2
        
        with patch("app.api.endpoints.users.get_db") as mock_get_db:
            mock_db = AsyncMock()
            mock_db.execute.side_effect = [mock_result, mock_count_result]
            mock_get_db.return_value.__aenter__.return_value = mock_db
            
            response = client.get(
                "/api/v1/users/mentions",
                params={"q": "test", "limit": 10}
            )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    
    assert "items" in data
    assert "next_cursor" in data
    assert "total_count" in data
    assert data["total_count"] == 2
    assert len(data["items"]) == 2
    
    # Check first user
    user1 = data["items"][0]
    assert user1["id"] == "1"
    assert user1["display_name"] == "Test User 1"
    assert user1["role_label"] is None
    assert user1["is_active"] is True
    
    # Check second user (admin)
    user2 = data["items"][1]
    assert user2["id"] == "2"
    assert user2["display_name"] == "Test User 2"
    assert user2["role_label"] == "管理员"
    assert user2["is_active"] is True


def test_search_mention_candidates_empty_query(client: TestClient):
    """Test mention search with empty query"""
    response = client.get(
        "/api/v1/users/mentions",
        params={"q": "", "limit": 10}
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_search_mention_candidates_no_results(client: TestClient):
    """Test mention search with no results"""
    with patch("app.api.endpoints.users.select") as mock_select:
        mock_query = MagicMock()
        mock_select.return_value = mock_query
        mock_query.where.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        
        # Mock empty results
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 0
        
        with patch("app.api.endpoints.users.get_db") as mock_get_db:
            mock_db = AsyncMock()
            mock_db.execute.side_effect = [mock_result, mock_count_result]
            mock_get_db.return_value.__aenter__.return_value = mock_db
            
            response = client.get(
                "/api/v1/users/mentions",
                params={"q": "nonexistent", "limit": 10}
            )
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    
    assert data["items"] == []
    assert data["total_count"] == 0
    assert data["next_cursor"] is None


# Note: The comment creation tests are complex integration tests that require
# mocking many dependencies. For now, we'll focus on testing the mention service
# and schema validation which are the core mention functionality.
# The comment creation with mentions is already tested through the mention service tests.
