"""Add mentions field to comments table (manual)

Revision ID: add_mentions_to_comments_manual
Revises: bf1d7dfea244
Create Date: 2025-11-04 01:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_mentions_to_comments_manual'
down_revision: Union[str, None] = 'bf1d7dfea244'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add mentions field to comments table
    op.add_column('comments', sa.Column('mentions', postgresql.JSONB().as_generic(sa.JSON), nullable=False, default=[], comment="被提及的用户ID列表"))


def downgrade() -> None:
    # Remove mentions field from comments table
    op.drop_column('comments', 'mentions')
