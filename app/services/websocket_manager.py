"""WebSocket连接管理器"""

import json
from datetime import datetime
from typing import Any

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.notifications.schemas import (
    NewNotificationMessage,
    NotificationResponse,
    SystemNotificationMessage,
    UnreadCountMessage,
    WebSocketMessage,
)


class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        # 存储活跃连接：{user_id: {connection_id: websocket}}
        self.active_connections: dict[int, dict[str, WebSocket]] = {}
        # 存储连接元数据：{connection_id: {"user_id": int, "connected_at": datetime}}
        self.connection_metadata: dict[str, dict[str, Any]] = {}
        self.logger = logger

    async def connect(self, websocket: WebSocket, user_id: int, connection_id: str):
        """建立WebSocket连接"""
        await websocket.accept()

        # 初始化用户连接字典
        if user_id not in self.active_connections:
            self.active_connections[user_id] = {}

        # 存储连接
        self.active_connections[user_id][connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "user_id": user_id,
            "connected_at": datetime.utcnow(),
        }

    def disconnect(self, user_id: int, connection_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            if connection_id in self.active_connections[user_id]:
                del self.active_connections[user_id][connection_id]

                # 如果用户没有其他连接，删除用户记录
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]

        # 删除连接元数据
        if connection_id in self.connection_metadata:
            del self.connection_metadata[connection_id]

        self.logger.info(f"WebSocket连接断开 - 用户ID: {user_id}, 连接ID: {connection_id}")

    async def send_personal_message(self, message: WebSocketMessage, user_id: int):
        """向特定用户发送消息"""
        if user_id not in self.active_connections:
            self.logger.debug(f"用户 {user_id} 没有活跃的WebSocket连接")
            return

        # 获取用户的所有连接
        user_connections = self.active_connections[user_id].copy()
        disconnected_connections = []

        for connection_id, websocket in user_connections.items():
            try:
                await websocket.send_text(message.model_dump_json())
                self.logger.debug(f"消息发送成功 - 用户ID: {user_id}, 连接ID: {connection_id}")
            except Exception:
                self.logger.exception(
                    "发送消息失败 - 用户ID: %s, 连接ID: %s", user_id, connection_id
                )
                disconnected_connections.append(connection_id)

        # 清理断开的连接
        for connection_id in disconnected_connections:
            self.disconnect(user_id, connection_id)

    async def send_notification(self, notification: NotificationResponse):
        """发送新通知消息"""
        message = NewNotificationMessage(data=notification)
        await self.send_personal_message(message, notification.user_id)

    async def send_unread_count_update(self, user_id: int, unread_count: int):
        """发送未读数量更新"""
        message = UnreadCountMessage(data={"unread_count": unread_count})
        await self.send_personal_message(message, user_id)

    async def send_system_notification(
        self, user_id: int, title: str, message: str, data: dict[str, Any] | None = None
    ):
        """发送系统通知"""
        notification_data = {
            "title": title,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
        }
        if data:
            notification_data.update(data)

        ws_message = SystemNotificationMessage(data=notification_data)
        await self.send_personal_message(ws_message, user_id)

    async def broadcast_system_message(
        self, title: str, message: str, data: dict[str, Any] | None = None
    ):
        """广播系统消息给所有在线用户"""
        notification_data = {
            "title": title,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
        }
        if data:
            notification_data.update(data)

        ws_message = SystemNotificationMessage(data=notification_data)

        # 向所有在线用户发送消息
        for user_id in list(self.active_connections.keys()):
            await self.send_personal_message(ws_message, user_id)

    def get_online_users(self) -> set[int]:
        """获取在线用户列表"""
        return set(self.active_connections.keys())

    def get_user_connection_count(self, user_id: int) -> int:
        """获取用户的连接数量"""
        if user_id in self.active_connections:
            return len(self.active_connections[user_id])
        return 0

    def get_total_connections(self) -> int:
        """获取总连接数"""
        return sum(len(connections) for connections in self.active_connections.values())

    def get_connection_stats(self) -> dict[str, Any]:
        """获取连接统计信息"""
        return {
            "total_connections": self.get_total_connections(),
            "online_users": len(self.active_connections),
            "users_with_multiple_connections": sum(
                1 for connections in self.active_connections.values() if len(connections) > 1
            ),
        }


# 全局WebSocket管理器实例
websocket_manager = ConnectionManager()


class WebSocketHandler:
    """WebSocket处理器"""

    def __init__(self, manager: ConnectionManager):
        self.manager = manager
        self.logger = logger

    async def handle_connection(
        self, websocket: WebSocket, user_id: int, connection_id: str, db: AsyncSession
    ):
        """处理WebSocket连接"""
        await self.manager.connect(websocket, user_id, connection_id)

        try:
            # 发送连接成功消息
            welcome_message = SystemNotificationMessage(
                data={
                    "title": "连接成功",
                    "message": "实时通知已启用",
                    "connection_id": connection_id,
                }
            )
            await websocket.send_text(welcome_message.model_dump_json())

            # 保持连接活跃
            while True:
                try:
                    # 等待客户端消息（心跳包等）
                    data = await websocket.receive_text()

                    # 处理心跳包
                    try:
                        message_data = json.loads(data)
                        if message_data.get("type") == "ping":
                            pong_message = WebSocketMessage(
                                type="pong", data={"timestamp": datetime.utcnow().isoformat()}
                            )
                            await websocket.send_text(pong_message.model_dump_json())
                    except json.JSONDecodeError:
                        self.logger.warning(f"收到无效的JSON消息: {data}")

                except WebSocketDisconnect as exc:
                    self.logger.info(
                        "WebSocket客户端断开 - 用户ID: %s, 连接ID: %s, code=%s, reason=%s",
                        user_id,
                        connection_id,
                        getattr(exc, "code", None),
                        getattr(exc, "reason", None),
                    )
                    break
                except Exception:
                    self.logger.exception(
                        "WebSocket处理错误 - 用户ID: %s, 连接ID: %s", user_id, connection_id
                    )
                    break

        except Exception:
            self.logger.exception(
                "WebSocket连接处理异常 - 用户ID: %s, 连接ID: %s", user_id, connection_id
            )
        finally:
            self.manager.disconnect(user_id, connection_id)


# 全局WebSocket处理器实例
websocket_handler = WebSocketHandler(websocket_manager)
