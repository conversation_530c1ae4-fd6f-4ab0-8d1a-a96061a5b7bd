"""通知相关的Celery任务"""

from textwrap import shorten
from typing import Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.async_runner import run_async
from app.core.celery import app
from app.core.logging import logger
from app.crud.notification import notification as crud_notification
from app.db.session import SessionLocal
from app.notifications.models import Notification, NotificationPriority, NotificationType
from app.notifications.schemas import NotificationResponse
from app.services.notification_service import NotificationService
from app.services.websocket_manager import websocket_manager

CONTENT_TYPE_LABELS = {
    "article": "文章",
    "video": "视频",
    "scratch": "Scratch 项目",
    "post": "沸点",
    "comment": "评论",
}


def _get_content_label(content_type: str) -> str:
    """根据内容类型获取用户可读标签"""
    return CONTENT_TYPE_LABELS.get(content_type, content_type)


def _build_content_action_url(content_type: str, content_id: int) -> str:
    """构建内容详情页的跳转链接"""
    normalized_type = (content_type or "").lower()
    if normalized_type == "article":
        return f"/articles/{content_id}"
    if normalized_type == "video":
        return f"/videos/{content_id}"
    if normalized_type == "scratch":
        return f"/scratch/{content_id}"
    if normalized_type == "post":
        return f"/posts/{content_id}"
    return f"/{normalized_type}/{content_id}"


@app.task(bind=True, name="app.tasks.notification_tasks.send_notification_websocket")
def send_notification_websocket(self, notification_id: int):
    """
    通过WebSocket发送通知

    Args:
        notification_id: 通知ID
    """
    try:
        run_async(_send_notification_websocket_async(notification_id))
        logger.info(f"WebSocket通知发送成功 - 通知ID: {notification_id}")
    except Exception as e:
        logger.error(f"WebSocket通知发送失败 - 通知ID: {notification_id}, 错误: {str(e)}")
        raise


async def _send_notification_websocket_async(notification_id: int):
    """异步发送WebSocket通知"""
    db = SessionLocal()
    try:
        # 获取通知详情
        stmt = select(Notification).where(Notification.id == notification_id)
        result = await db.execute(stmt)
        notification = result.scalar_one_or_none()

        if not notification:
            logger.warning(f"通知不存在 - ID: {notification_id}")
            return

        # 转换为响应模型
        notification_response = NotificationResponse.model_validate(notification)

        # 通过WebSocket发送通知
        await websocket_manager.send_notification(notification_response)

        # 发送未读数量更新
        unread_count = await crud_notification.get_unread_count(db=db, user_id=notification.user_id)
        await websocket_manager.send_unread_count_update(
            user_id=notification.user_id, unread_count=unread_count
        )

    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.update_unread_count")
def update_unread_count(self, user_id: int):
    """
    更新用户未读通知数量

    Args:
        user_id: 用户ID
    """
    try:
        run_async(_update_unread_count_async(user_id))
        logger.info(f"未读数量更新成功 - 用户ID: {user_id}")
    except Exception as e:
        logger.error(f"未读数量更新失败 - 用户ID: {user_id}, 错误: {str(e)}")
        raise


async def _update_unread_count_async(user_id: int):
    """异步更新未读数量"""
    db = SessionLocal()
    try:
        unread_count = await crud_notification.get_unread_count(db=db, user_id=user_id)

        # 通过WebSocket发送未读数量更新
        await websocket_manager.send_unread_count_update(user_id=user_id, unread_count=unread_count)

    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.create_content_notification")
def create_content_notification(self, content_type: str, content_id: int):
    """
    创建内容相关通知

    Args:
        content_type: 内容类型 (article, video)
        content_id: 内容ID
    """
    try:
        run_async(_create_content_notification_async(content_type, content_id))
        logger.info(f"内容通知创建成功 - 类型: {content_type}, ID: {content_id}")
    except Exception as e:
        logger.error(f"内容通知创建失败 - 类型: {content_type}, ID: {content_id}, 错误: {str(e)}")
        raise


async def _create_content_notification_async(content_type: str, content_id: int):
    """异步创建内容通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        normalized_type = (content_type or "").lower()

        if normalized_type == "article":
            from app.crud.article import article as crud_article

            content = await crud_article.get(db, id=content_id)
            if content:
                await _notify_followers_about_new_content(
                    db=db,
                    notification_service=notification_service,
                    author_id=content.author_id,
                    content_type="article",
                    content_title=content.title,
                    content_id=content_id,
                )
        elif normalized_type == "video":
            from app.crud.video import video as crud_video

            content = await crud_video.get(db, id=content_id)
            if content:
                await _notify_followers_about_new_content(
                    db=db,
                    notification_service=notification_service,
                    author_id=content.author_id,
                    content_type="video",
                    content_title=content.title,
                    content_id=content_id,
                )
        elif normalized_type == "scratch":
            from app.crud.scratch import scratch_product as crud_scratch

            content = await crud_scratch.get(db, id=content_id)
            if content and content.is_published:
                await _notify_followers_about_new_content(
                    db=db,
                    notification_service=notification_service,
                    author_id=content.author_id,
                    content_type="scratch",
                    content_title=content.title,
                    content_id=content_id,
                )
        else:
            logger.warning(
                "暂不支持的内容类型通知: {} (content_id={})", normalized_type, content_id
            )

    finally:
        await db.close()


async def _notify_followers_about_new_content(
    db: AsyncSession,
    notification_service: NotificationService,
    *,
    author_id: int,
    content_type: str,
    content_title: str,
    content_id: int,
) -> None:
    """通知关注该作者的用户有新内容"""
    from app.crud.user import user as crud_user

    # 获取作者信息
    author = await crud_user.get(db, id=author_id)
    if not author:
        return

    # 获取关注者列表
    followers = await crud_user.get_followers(db, user_id=author_id)

    if not followers:
        return

    label = _get_content_label(content_type)
    action_url = _build_content_action_url(content_type, content_id)

    # 为每个关注者创建通知
    for follower in followers:
        try:
            await notification_service.create_content_recommendation_notification(
                db=db,
                user_id=follower.id,
                content_title=content_title,
                content_type=content_type,
                content_id=content_id,
                content_label=label,
                action_url=action_url,
            )
        except Exception as e:
            logger.error(f"为用户 {follower.id} 创建内容通知失败: {str(e)}")


@app.task(bind=True, name="app.tasks.notification_tasks.cleanup_expired_notifications")
def cleanup_expired_notifications(self):
    """清理过期通知"""
    try:
        count = run_async(_cleanup_expired_notifications_async())
        logger.info(f"过期通知清理完成 - 清理数量: {count}")
        return count
    except Exception as e:
        logger.error(f"过期通知清理失败: {str(e)}")
        raise


async def _cleanup_expired_notifications_async() -> int:
    """异步清理过期通知"""
    db = SessionLocal()
    try:
        count = await crud_notification.cleanup_expired_notifications(db)
        return count
    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.send_system_broadcast")
def send_system_broadcast(self, title: str, message: str, data: dict[str, Any] = None):
    """
    发送系统广播通知

    Args:
        title: 通知标题
        message: 通知内容
        data: 附加数据
    """
    try:
        run_async(_send_system_broadcast_async(title, message, data))
        logger.info(f"系统广播发送成功 - 标题: {title}")
    except Exception as e:
        logger.error(f"系统广播发送失败 - 标题: {title}, 错误: {str(e)}")
        raise


async def _send_system_broadcast_async(title: str, message: str, data: dict[str, Any] = None):
    """异步发送系统广播"""
    await websocket_manager.broadcast_system_message(title=title, message=message, data=data)


# 定期任务：清理过期通知
@app.task(bind=True, name="app.tasks.notification_tasks.periodic_cleanup")
def periodic_cleanup_notifications(self):
    """定期清理过期通知（每天执行）"""
    try:
        count = cleanup_expired_notifications.delay()
        logger.info("定期清理任务已启动")
        return count
    except Exception as e:
        logger.error(f"定期清理任务启动失败: {str(e)}")
        raise


@app.task(bind=True, name="app.tasks.notification_tasks.broadcast_notification_task")
def broadcast_notification_task(self, task_data: dict[str, Any]):
    """
    广播通知任务（异步处理）

    Args:
        task_data: 包含通知信息的字典
    """
    try:
        result = run_async(_broadcast_notification_async(task_data))
        logger.info(f"广播通知任务完成 - 标题: {task_data.get('title')}, 创建数量: {result}")
        return result
    except Exception as e:
        logger.error(f"广播通知任务失败 - 标题: {task_data.get('title')}, 错误: {str(e)}")
        raise


async def _broadcast_notification_async(task_data: dict[str, Any]) -> int:
    """异步执行广播通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        from sqlalchemy import select

        from app.models.user import User

        notification_type_value = task_data.get("notification_type")
        priority_value = task_data.get("priority")
        extra_data = task_data.get("data")
        if not isinstance(extra_data, dict):
            extra_data = {}

        # 获取目标用户类型
        target_user_type = task_data.get("target_user_type", "all")

        # 构建用户查询
        if target_user_type == "all":
            stmt = select(User.id).where(User.is_active == True, User.is_deleted == False)
        elif target_user_type == "active":
            # 可以根据需要添加更多筛选条件
            stmt = select(User.id).where(User.is_active == True, User.is_deleted == False)
        else:
            stmt = select(User.id).where(User.is_active == True, User.is_deleted == False)

        # 分批处理用户
        BATCH_SIZE = 1000
        offset = 0
        total_created = 0

        while True:
            # 获取一批用户ID
            batch_stmt = stmt.limit(BATCH_SIZE).offset(offset)
            result = await db.execute(batch_stmt)
            user_ids = [row[0] for row in result.fetchall()]

            if not user_ids:
                break

            # 为这批用户创建通知
            batch_created = await _create_notifications_batch(db, user_ids, task_data)
            total_created += batch_created

            offset += BATCH_SIZE

            # 避免长时间占用数据库连接
            if offset % (BATCH_SIZE * 5) == 0:
                await db.commit()
                logger.info(f"已处理 {offset} 个用户，创建通知 {total_created} 条")

        await db.commit()
        logger.info(f"广播通知完成 - 总计创建 {total_created} 条通知")

        if task_data.get("broadcast_websocket_on_execute"):
            message_data = {
                "type": notification_type_value,
                "priority": priority_value,
                "action_url": task_data.get("action_url"),
            }
            message_data.update(extra_data)

            await websocket_manager.broadcast_system_message(
                title=task_data.get("title", ""),
                message=task_data.get("message", ""),
                data=message_data,
            )

            logger.info(
                "定时广播通知已发送实时推送 - 标题: {}, 在线连接数: {}",
                task_data.get("title"),
                websocket_manager.get_total_connections(),
            )

        return total_created

    finally:
        await db.close()


async def _create_notifications_batch(
    db: AsyncSession, user_ids: list[int], task_data: dict[str, Any]
) -> int:
    """批量创建通知"""
    try:
        from datetime import datetime, timedelta

        from app.notifications.models import Notification, NotificationPriority, NotificationType

        # 解析任务数据
        notification_type = NotificationType(task_data["notification_type"])
        priority = NotificationPriority(task_data["priority"])

        # 计算过期时间
        expires_at = None
        if task_data.get("expires_in_hours"):
            expires_at = datetime.utcnow() + timedelta(hours=task_data["expires_in_hours"])

        # 准备批量插入数据
        notifications_data = []
        for user_id in user_ids:
            notification_data = {
                "user_id": user_id,
                "type": notification_type,
                "priority": priority,
                "title": task_data["title"],
                "message": task_data["message"],
                "data": task_data.get("data"),
                "action_url": task_data.get("action_url"),
                "expires_at": expires_at,
                "created_at": datetime.utcnow(),
            }
            notifications_data.append(notification_data)

        # 批量插入
        from sqlalchemy import insert

        stmt = insert(Notification).values(notifications_data)
        await db.execute(stmt)

        # 为每个用户创建WebSocket推送事件
        for user_id in user_ids:
            # 创建发件箱消息用于WebSocket推送
            from app.models.outbox import OutboxMessage

            outbox_msg = OutboxMessage(
                topic="notification.created",
                payload={
                    "user_id": user_id,
                    "type": notification_type.value,
                    "priority": priority.value,
                    "title": task_data["title"],
                    "message": task_data["message"],
                },
            )
            db.add(outbox_msg)

        return len(user_ids)

    except Exception as e:
        logger.error(f"批量创建通知失败: {str(e)}")
        raise


@app.task(bind=True, name="app.tasks.notification_tasks.create_like_notification")
def create_like_notification(self, liker_user_id: int, content_type: str, content_id: int):
    """
    创建点赞通知

    Args:
        liker_user_id: 点赞用户ID
        content_type: 内容类型 (article, video)
        content_id: 内容ID
    """
    try:
        run_async(_create_like_notification_async(liker_user_id, content_type, content_id))
        logger.info(
            f"点赞通知创建成功 - 点赞者: {liker_user_id}, 内容: {content_type}:{content_id}"
        )
    except Exception as e:
        logger.error(
            f"点赞通知创建失败 - 点赞者: {liker_user_id}, 内容: {content_type}:{content_id}, 错误: {str(e)}"
        )
        raise


async def _create_like_notification_async(liker_user_id: int, content_type: str, content_id: int):
    """异步创建点赞通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        normalized_type = (content_type or "").lower()
        content_author_id = None
        content_title = ""
        content_label = _get_content_label(normalized_type)
        action_url: str | None = _build_content_action_url(normalized_type, content_id)
        extra_data: dict[str, Any] = {}

        if normalized_type == "article":
            from app.crud.article import article as crud_article

            content = await crud_article.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.title
        elif normalized_type == "video":
            from app.crud.video import video as crud_video

            content = await crud_video.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.title
        elif normalized_type == "scratch":
            from app.crud.scratch import scratch_product as crud_scratch

            content = await crud_scratch.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.title
        elif normalized_type == "post":
            from app.crud.post import post as crud_post

            content = await crud_post.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.content or ""
        elif normalized_type == "comment":
            from app.crud.comment import comment as crud_comment

            comment = await crud_comment.get(db, id=content_id)
            if comment:
                content_author_id = comment.author_id
                base_type = comment.comment_type.value
                base_id = comment.article_id or comment.video_id or comment.scratch_id
                content_label = _get_content_label("comment")
                content_title = shorten(comment.content or "评论", width=40, placeholder="...")
                if base_id:
                    action_url = (
                        f"{_build_content_action_url(base_type, base_id)}#comment-{comment.id}"
                    )
                else:
                    action_url = None
                extra_data.update(
                    {
                        "parent_content_type": base_type,
                        "parent_content_id": base_id,
                    }
                )

        # 如果找不到内容或者是自己点赞自己的内容，不发送通知
        if not content_author_id or content_author_id == liker_user_id:
            return

        # 获取点赞用户信息
        from app.crud.user import user as crud_user

        liker = await crud_user.get(db, id=liker_user_id)
        if not liker:
            return

        notification_data = {
            "liker_user_id": liker_user_id,
            "liker_username": liker.username,
            "content_type": normalized_type,
            "content_id": content_id,
            "content_title": content_title,
            "content_label": content_label,
        }
        notification_data.update(extra_data)

        # 创建点赞通知
        await notification_service.create_notification(
            db=db,
            user_id=content_author_id,
            notification_type=NotificationType.NEW_MESSAGE,  # 可以考虑添加专门的点赞类型
            title="收到新点赞",
            message=f"{liker.username} 点赞了您的{content_label}「{content_title}」",
            priority=NotificationPriority.LOW,
            data=notification_data,
            action_url=action_url,
            expires_in_hours=72,  # 3天后过期
        )

    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.create_comment_notification")
def create_comment_notification(
    self, comment_id: int, author_id: int, content_type: str, content_id: int
):
    """
    创建评论通知

    Args:
        comment_id: 评论ID
        author_id: 评论作者ID
        content_type: 内容类型 (article, video)
        content_id: 内容ID
    """
    try:
        run_async(
            _create_comment_notification_async(comment_id, author_id, content_type, content_id)
        )
        logger.info(f"评论通知创建成功 - 评论ID: {comment_id}, 内容: {content_type}:{content_id}")
    except Exception as e:
        logger.error(f"评论通知创建失败 - 评论ID: {comment_id}, 错误: {str(e)}")
        raise


async def _create_comment_notification_async(
    comment_id: int, author_id: int, content_type: str, content_id: int
):
    """异步创建评论通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        normalized_type = (content_type or "").lower()
        content_author_id = None
        content_title = ""
        content_label = _get_content_label(normalized_type)

        if normalized_type == "article":
            from app.crud.article import article as crud_article

            content = await crud_article.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.title
        elif normalized_type == "video":
            from app.crud.video import video as crud_video

            content = await crud_video.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.title
        elif normalized_type == "scratch":
            from app.crud.scratch import scratch_product as crud_scratch

            content = await crud_scratch.get(db, id=content_id)
            if content:
                content_author_id = content.author_id
                content_title = content.title

        # 如果找不到内容或者是自己评论自己的内容，不发送通知
        if not content_author_id or content_author_id == author_id:
            return

        # 获取评论作者信息
        from app.crud.user import user as crud_user

        commenter = await crud_user.get(db, id=author_id)
        if not commenter:
            return

        action_url = (
            f"{_build_content_action_url(normalized_type, content_id)}#comment-{comment_id}"
        )

        # 创建评论通知
        await notification_service.create_notification(
            db=db,
            user_id=content_author_id,
            notification_type=NotificationType.NEW_MESSAGE,
            title="收到新评论",
            message=f"{commenter.username} 评论了您的{content_label}「{content_title}」",
            priority=NotificationPriority.NORMAL,
            data={
                "comment_id": comment_id,
                "commenter_user_id": author_id,
                "commenter_username": commenter.username,
                "content_type": normalized_type,
                "content_id": content_id,
                "content_label": content_label,
                "content_title": content_title,
            },
            action_url=action_url,
            expires_in_hours=168,  # 7天后过期
        )

    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.create_reply_notification")
def create_reply_notification(
    self, comment_id: int, author_id: int, reply_to_id: int, content_type: str, content_id: int
):
    """
    创建回复通知

    Args:
        comment_id: 回复评论ID
        author_id: 回复作者ID
        reply_to_id: 被回复的评论ID
        content_type: 内容类型 (article, video)
        content_id: 内容ID
    """
    try:
        run_async(
            _create_reply_notification_async(
                comment_id, author_id, reply_to_id, content_type, content_id
            )
        )
        logger.info(f"回复通知创建成功 - 回复ID: {comment_id}, 被回复评论: {reply_to_id}")
    except Exception as e:
        logger.error(f"回复通知创建失败 - 回复ID: {comment_id}, 错误: {str(e)}")
        raise


async def _create_reply_notification_async(
    comment_id: int, author_id: int, reply_to_id: int, content_type: str, content_id: int
):
    """异步创建回复通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        # 获取被回复的评论信息
        from app.crud.comment import comment as crud_comment

        original_comment = await crud_comment.get(db, id=reply_to_id)
        if not original_comment or original_comment.author_id == author_id:
            return  # 如果评论不存在或者是自己回复自己，不发送通知

        # 获取回复者信息
        from app.crud.user import user as crud_user

        replier = await crud_user.get(db, id=author_id)
        if not replier:
            return

        normalized_type = (content_type or "").lower()

        # 获取内容标题
        content_title = ""
        if normalized_type == "article":
            from app.crud.article import article as crud_article

            content = await crud_article.get(db, id=content_id)
            if content:
                content_title = content.title
        elif normalized_type == "video":
            from app.crud.video import video as crud_video

            content = await crud_video.get(db, id=content_id)
            if content:
                content_title = content.title
        elif normalized_type == "scratch":
            from app.crud.scratch import scratch_product as crud_scratch

            content = await crud_scratch.get(db, id=content_id)
            if content:
                content_title = content.title

        content_label = _get_content_label(normalized_type)
        action_url = (
            f"{_build_content_action_url(normalized_type, content_id)}#comment-{comment_id}"
        )

        # 创建回复通知
        await notification_service.create_notification(
            db=db,
            user_id=original_comment.author_id,
            notification_type=NotificationType.NEW_MESSAGE,
            title="收到新回复",
            message=f"{replier.username} 回复了您在「{content_title}」中的评论",
            priority=NotificationPriority.NORMAL,
            data={
                "reply_comment_id": comment_id,
                "original_comment_id": reply_to_id,
                "replier_user_id": author_id,
                "replier_username": replier.username,
                "content_type": normalized_type,
                "content_id": content_id,
                "content_label": content_label,
                "content_title": content_title,
            },
            action_url=action_url,
            expires_in_hours=168,  # 7天后过期
        )

    finally:
        await db.close()
