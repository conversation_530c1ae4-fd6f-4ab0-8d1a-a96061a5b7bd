from typing import Any

from sqlalchemy import func, select, delete
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.crud.base import CRUDBase
from app.models import Video
from app.models.outbox import OutboxMessage
from app.models.user import User, user_follow
from app.schemas.user import UserBase, UserCreate, UserUpdate


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    async def get_paginated_users(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        filters: dict[str, Any] | None = None,
        include_total: bool = False,
    ) -> CursorPaginationResponse[UserBase]:
        """获取分页的用户列表"""
        query = select(self.model).where(self.model.is_deleted.is_(False))

        if filters:
            if "is_active" in filters:
                query = query.where(self.model.is_active == filters["is_active"])
            if "is_superuser" in filters:
                query = query.where(self.model.is_superuser == filters["is_superuser"])

        total_count = None
        if include_total:
            count_query = select(func.count()).select_from(query.subquery())
            total_count = (await db.execute(count_query)).scalar_one()

        if params.cursor:
            cursor_value = int(params.cursor)
            if params.order_direction == "desc":
                query = query.where(self.model.id < cursor_value)
            else:
                query = query.where(self.model.id > cursor_value)

        order_column = getattr(self.model, params.order_by, self.model.id)
        if params.order_direction == "desc":
            query = query.order_by(order_column.desc())
        else:
            query = query.order_by(order_column.asc())

        query = query.limit(params.size + 1)
        result = await db.execute(query)
        items = result.scalars().all()

        has_more = len(items) > params.size
        if has_more:
            items = items[: params.size]

        next_cursor = str(items[-1].id) if has_more else None

        return CursorPaginationResponse(
            items=items,
            total_count=total_count,
            has_next=has_more,
            has_previous=params.cursor is not None,
            next_cursor=next_cursor,
            previous_cursor=params.cursor,
        )

    async def create(self, db: AsyncSession, *, obj_in: UserCreate, commit: bool = True) -> User:
        """创建用户，并原子性地创建发件箱消息"""
        if isinstance(obj_in, dict):
            obj_data = obj_in.copy()
        else:
            obj_data = obj_in.model_dump(exclude_unset=True)
        allowed_columns = set(self.model.__table__.columns.keys())
        filtered_data = {key: value for key, value in obj_data.items() if key in allowed_columns}
        db_obj = self.model(**filtered_data)
        db.add(db_obj)

        # 创建发件箱消息
        self._create_event(db, topic="user.created", payload={"user_id": db_obj.id})

        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def update(
        self, db: AsyncSession, *, db_obj: User, obj_in: UserUpdate | dict[str, Any]
    ) -> User:
        """更新用户，并原子性地创建发件箱消息"""
        # 更新版本号
        db_obj.cache_version += 1

        # 调用基类的更新方法
        updated_user = await super().update(db, db_obj=db_obj, obj_in=obj_in, commit=False)

        # 创建发件箱消息
        self._create_event(db, topic="user.updated", payload={"user_id": updated_user.id})

        await db.commit()
        await db.refresh(updated_user)
        return updated_user

    async def remove(self, db: AsyncSession, *, id: int) -> User | None:
        """删除用户（软删除），并原子性地创建发件箱消息"""
        db_obj = await self.get(db, id)
        if not db_obj:
            return None

        db_obj.is_deleted = True
        db_obj.cache_version += 1
        db.add(db_obj)

        await db.commit()
        return db_obj

    async def get_by_id(self, db: AsyncSession, *, id: int) -> User | None:
        """通过ID获取用户"""
        query = select(self.model).filter(self.model.id == id, self.model.is_deleted.is_(False))
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_by_username(self, db: AsyncSession, *, username: str) -> User | None:
        """通过用户名获取用户"""
        query = (
            select(self.model)
            .options(selectinload(self.model.devices))
            .filter(self.model.username == username, self.model.is_deleted.is_(False))
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_by_openid(self, db: AsyncSession, *, openid: str) -> User | None:
        """通过微信openid获取用户"""
        query = (
            select(self.model)
            .options(selectinload(self.model.devices))
            .filter(self.model.wechat_openid == openid, self.model.is_deleted.is_(False))
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[User]:
        """根据ID列表获取多个用户"""
        if not ids:
            return []
        query = select(self.model).where(self.model.id.in_(ids), self.model.is_deleted.is_(False))
        result = await db.execute(query)
        return result.scalars().all()

    async def is_admin(self, user: User | None) -> bool:
        """检查用户是否为管理员

        Args:
            user: 用户对象

        Returns:
            bool: 是否为管理员
        """
        if not user:
            return False
        return user.is_superuser

    async def get_top_authors_in_category(
        self, db: AsyncSession, *, category_id: int, limit: int = 5
    ):
        # 获取该分类下发布视频最多的作者ID
        top_author_query = (
            select(Video.author_id, func.count(Video.id).label("video_count"))
            .filter(Video.category_id == category_id)
            .group_by(Video.author_id)
            .order_by(func.count(Video.id).desc())
            .limit(limit)
        )
        result = await db.execute(top_author_query)
        top_author_ids = result.all()

        author_ids = [item.author_id for item in top_author_ids]
        if not author_ids:
            return []

        # 根据作者ID获取用户信息
        authors_query = select(User).filter(User.id.in_(author_ids), User.is_deleted.is_(False))
        result = await db.execute(authors_query)
        return result.scalars().all()

    async def follow(self, db: AsyncSession, *, follower: User, followed: User) -> None:
        """
        处理用户关注逻辑，并在同一事务中创建统计更新事件。
        """
        # 通过中间表插入关注关系，避免异步会话的绿色线程限制
        stmt = insert(user_follow).values(follower_id=follower.id, followed_id=followed.id)
        stmt = stmt.on_conflict_do_nothing()
        result = await db.execute(stmt)
        if result.rowcount == 0:
            return

        # 为关注者创建事件
        follower_event = OutboxMessage(
            topic="stats.update",
            payload={
                "entity_type": "user",
                "entity_id": follower.id,
                "field_name": "following_count",
                "value_change": 1,
            },
        )
        db.add(follower_event)

        # 为被关注者创建事件
        followed_event = OutboxMessage(
            topic="stats.update",
            payload={
                "entity_type": "user",
                "entity_id": followed.id,
                "field_name": "follower_count",
                "value_change": 1,
            },
        )
        db.add(followed_event)

        # 触发推荐缓存失效事件
        recommendation_event = OutboxMessage(
            topic="recommendation.user_followed",
            payload={
                "follower_id": follower.id,
                "followed_id": followed.id,
            },
        )
        db.add(recommendation_event)

        await db.commit()

    async def unfollow(self, db: AsyncSession, *, follower: User, followed: User) -> None:
        """
        处理取消关注逻辑，并在同一事务中创建统计更新事件。
        """
        stmt = (
            delete(user_follow)
            .where(user_follow.c.follower_id == follower.id)
            .where(user_follow.c.followed_id == followed.id)
        )
        result = await db.execute(stmt)
        if result.rowcount == 0:
            return

        # 为关注者创建事件
        follower_event = OutboxMessage(
            topic="stats.update",
            payload={
                "entity_type": "user",
                "entity_id": follower.id,
                "field_name": "following_count",
                "value_change": -1,
            },
        )
        db.add(follower_event)

        # 为被关注者创建事件
        followed_event = OutboxMessage(
            topic="stats.update",
            payload={
                "entity_type": "user",
                "entity_id": followed.id,
                "field_name": "follower_count",
                "value_change": -1,
            },
        )
        db.add(followed_event)

        await db.commit()


user = CRUDUser(User)
